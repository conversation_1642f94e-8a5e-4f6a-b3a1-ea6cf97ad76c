// Marker Definitions
var actionMarkers = {
  one: { element: document.querySelector("#one"), visible: false },
  two: { element: document.querySelector("#two"), visible: false },
  three: { element: document.querySelector("#three"), visible: false },
  four: { element: document.querySelector("#four"), visible: false },
  boat: { element: document.querySelector("#boat"), visible: false },
  bus: { element: document.querySelector("#bus"), visible: false },
  pocket: { element: document.querySelector("#pocket"), visible: false },
  coin: { element: document.querySelector("#coin"), visible: false },
  ball: { element: document.querySelector("#ball"), visible: false },
  ballOne: { element: document.querySelector("#ball-one"), visible: false },
  ticket: { element: document.querySelector("#ticket"), visible: false },
};

var currentScene = "vendingMachine";
var subSceneIndex = 0;

// Initialize the game
resetGame();

// Event Listeners for Markers
function setupEventListeners() {
  Object.values(actionMarkers).forEach((marker) => {
    marker.element.addEventListener("markerFound", () => {
      console.log(marker.element.id + " found"); // Debugging
      marker.visible = true;
      handleMarkerInteraction(marker.element.id);
    });
    marker.element.addEventListener("markerLost", () => {
      marker.visible = false;
    });
  });
}


actionMarkers.ballOne.element.addEventListener("markerFound", () => {
  console.log("ball-one found");
  actionMarkers.ballOne.visible = true;
  handleMarkerInteraction("ball-one");
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
  let currentActions = scenes[currentScene]?.actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    console.log("Executing action for " + markerId); // Debugging
    currentActions[markerId]();
  } else {
    console.log(`No action defined for ${markerId} in scene ${currentScene}, subScene ${subSceneIndex}`);
  }
}


function resetCurrentSceneToInitialSubscene() {
  // Reset all relevant objects and state for the current scene
  resetCylinderPosition(); // Reset the cylinder to its initial position
  resetCubePosition();
  resetConePosition(); // Add any additional reset logic here for other objects or state variables
  resetBallPosition();

  subSceneIndex = 0; // Reset to the first subscene

  // If you have any visible/invisible toggles based on subscenes, reset those here
  // For example:
  // document.querySelector("#cilindrs").setAttribute("visible", true);
  // Adjust visibility as needed for your scene setup

console.log(`Scene updated to: ${currentScene}, Subscene: ${subSceneIndex}`);


var scenes = {
  vendingMachine: {
    actions: [
      {
        // Subscene 1 (skapī ir dēlis)
        // Ensure that this function is correctly referenced in your scenes object
        pocket: () => moveToSubScene(1, insertCoin),
        coin: () => {}, // No action
        ball: () => {}, // No action
      },
      {
        // Subscene 2 (skrituļdēlis)
        pocket: () => {}, // No action
        coin: () => completeScene("ballOne", showBallOne, hideVendingMachine),
        ball: () => resetCurrentSceneToInitialSubscene(),
      },
    ],
  },
  ballOne: {
    actions: [
      {
        marker1: () => {},
        "kubs-marker": () => completeScene("cube", hideBallOne, showCube),
        marker3: () => {},
      },
    ],
  },

  cube: {
    actions: [
      {
        // Subscene 1
        marker2: () => moveToSubScene(1, moveCubeX),
        marker1: () => {}, // No action
        marker3: () => {}, // No action
      },
      {
        // Subscene 2
        marker2: () => {}, // No action
        marker1: () => moveToSubScene(2, moveCubeZ),
        marker3: () => resetCurrentSceneToInitialSubscene(),
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => {}, // No action
        marker3: () => completeScene("cone", hideCube, showCone),
      },
    ],
  },
  cone: {
    actions: [
      {
        // Subscene 1
        marker2: () => {}, // No action,
        marker1: () => {}, // No action
        marker3: () => moveToSubScene(1, moveConeX),
      },
      {
        // Subscene 2
        marker2: () => moveToSubScene(2, moveConeZ),
        marker1: () => resetCurrentSceneToInitialSubscene(),
        marker3: () => {}, // No action
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => completeScene("cone", hideCone),
        marker3: () => {}, // No action
      },
    ],
  },
};

function animateMove(object, targetPosition, duration) {
  let startTime = null;
  const startPosition = object.getAttribute("position");

  function animate(time) {
    if (!startTime) startTime = time;
    const elapsedTime = time - startTime;
    const fraction = elapsedTime / duration;

    if (fraction < 1) {
      let newX =
        startPosition.x + (targetPosition.x - startPosition.x) * fraction;
      object.setAttribute("position", {
        x: newX,
        y: startPosition.y,
        z: startPosition.z,
      });
      requestAnimationFrame(animate);
    } else {
      // Ensure the final position is set
      object.setAttribute("position", targetPosition);
    }
  }

  requestAnimationFrame(animate);
}

function insertCoin() {
  let cylinder = document.querySelector("#vending-machine");
  let currentPosition = cylinder.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  console.log("Coin inserted");
  animateMove(cylinder, targetPosition, 1000); // 1000 ms for the animation
}

function showBallOne() {
  let ballOne = document.querySelector("#ball-one");
  if (ballOne) {
    ballOne.setAttribute("visible", true);
  } else {
    console.error("Element #ball-one not found");
  }
}

function moveCylinderZ() {
  let cylinder = document.querySelector("#vending-machine");
  let currentPosition = cylinder.getAttribute("position");
  cylinder.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}

function moveCubeX() {
  let cube = document.querySelector("#kubs");
  let currentPosition = cube.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  animateMove(cube, targetPosition, 1000); // 1000 ms for the animation
}

function moveCubeZ() {
  let cube = document.querySelector("#kubs");
  let currentPosition = cube.getAttribute("position");
  cube.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveConeX() {
  let cone = document.querySelector("#konuss");
  let currentPosition = cone.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  animateMove(cone, targetPosition, 1000); // 1000 ms for the animation
}

function moveConeZ() {
  let cone = document.querySelector("#konuss");
  let currentPosition = cone.getAttribute("position");
  cone.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;
  resetCylinderPosition(); // This will reset the position
  resetCubePosition();
  resetConePosition();
  resetBallPosition();
}

function resetCylinderPosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#vending-machine")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function resetCubePosition() {
  document
    .querySelector("#kubs")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function resetConePosition() {
  document
    .querySelector("#konuss")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function resetBallPosition() {
  document
    .querySelector("#ball")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function completeScene(nextScene, ...actions) {
  currentScene = nextScene;
  subSceneIndex = 0;
  actions.forEach((action) => action());
}
function hideVendingMachine() {
  let cylinder = document.querySelector("#vending-machine");
  if (cylinder) {
    cylinder.setAttribute("visible", false);
  } else {
    console.error("Cylinder #vending-machine not found");
  }
}

function hideBallOne() {
  let ballOne = document.querySelector("#ball-one");
  if (ballOne) {
    ballOne.setAttribute("visible", false);
  } else {
    console.error("Element #ball-one not found");
  }
}

function hideBall() {
  let ball = document.querySelector("#ball");
  if (ball) {
    ball.setAttribute("visible", false);
  } else {
    console.error("Element #ball not found");
  }
}

function hideCube() {
  let kubs = document.querySelector("#kubs");
  if (kubs) {
    kubs.setAttribute("visible", false);
  } else {
    console.error("Element #kubs not found");
  }
}

function showCube() {
  document.querySelector("#kubs").setAttribute("visible", true);
}
function hideCone() {
  document.querySelector("#konuss").setAttribute("visible", false);
}

function showCone() {
  document.querySelector("#konuss").setAttribute("visible", true);
}
function showBall() {
  document.querySelector("#ball").setAttribute("visible", true);
}
function resetScene() {
  subSceneIndex = 0;
  resetCylinderPosition();
  resetCubePosition();
  resetConePosition();
  resetBallPosition();
  // Reset other objects if necessary
}

function resetGame() {
  currentScene = "vendingMachine"; // Set to your initial scene
  subSceneIndex = 0;
  document.querySelector("#kubs").setAttribute("visible", false); // Explicitly hide the cube
  document.querySelector("#konuss").setAttribute("visible", false);
  document.querySelector("#vending-machine").setAttribute("visible", true); // Show cylinder initially
  document.querySelector("#ball-one").setAttribute("visible", false); // Hide ball one initially
  document.querySelector("#ball").setAttribute("visible", false); // Hide ball initially
  resetCylinderPosition();
  resetCubePosition();
  resetConePosition();
  resetBallPosition();

  // Reset the scene and subscene indexes

  updateSceneInfoDisplay(); // Make sure this is called at the end

  // Reset any other game states as needed
}

// A-Frame Component for Continuous Tracking
AFRAME.registerComponent("continuous-tracking", {
  tick: function () {
    // Optional: Continuous tracking logic
  },
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");

setupEventListeners();