<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <title>AR.js + A-Frame (4x4_BCH_13_5_5)</title>
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <!-- A-Frame + maintained AR.js -->
  <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
  <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js/aframe/build/aframe-ar.min.js"></script>

  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <!-- Inventory UI -->
  <div id="inventory">
    <div class="inventory-slot" id="slot-1"></div>
    <div class="inventory-slot" id="slot-2"></div>
    <div class="inventory-slot" id="slot-3"></div>
    <div class="inventory-slot" id="slot-4"></div>
  </div>
  <button id="collectButton" style="display:none;">Collect</button>
  <div id="doorDrop" class="drop-zone" style="display:none;">
    <span>Drop KEY here to open the DOOR</span>
  </div>

  <!-- AR Scene -->
  <a-scene
    embedded
    vr-mode-ui="enabled: false"
    renderer="precision: medium; antialias: true"
    arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_5_5; debugUIEnabled: false;"
  >
    <!-- KEY marker (25) -->
    <a-marker type="barcode" value="25" id="marker-key">
      <a-entity id="key-entity" class="collectable-item" data-item="key" scale="0.4 0.4 0.4">
        <!-- simple “key” -->
        <a-cylinder position="0 0.2 0" height="0.8" radius="0.05" color="#FFD700"></a-cylinder>
        <a-torus position="0 0.6 0" radius="0.18" radius-tubular="0.03" rotation="90 0 0" color="#FFD700"></a-torus>
        <a-box position="0.25 0.1 0" width="0.18" height="0.06" depth="0.06" color="#FFD700"></a-box>
        <a-box position="0.25 0.2 0" width="0.12" height="0.06" depth="0.06" color="#FFD700"></a-box>
      </a-entity>
    </a-marker>

    <!-- DOOR marker (24) -->
    <a-marker type="barcode" value="24" id="marker-door">
      <a-entity id="door-entity" class="interactive-item" data-item="door" scale="0.6 0.6 0.2">
        <a-box width="1.2" height="2.2" depth="0.1" position="0 1.1 -0.05" color="#3E2C1C"></a-box>
        <a-box id="door-leaf" width="1" height="2" depth="0.08" position="0 1 0" color="#8B5A2B"
               animation__open="property: rotation; startEvents: open; to: 0 80 0; dur: 900; easing: easeOutQuad">
        </a-box>
        <a-sphere radius="0.05" position="0.4 1 0.05" color="#FFD700"></a-sphere>
      </a-entity>
    </a-marker>

    <!-- TREASURE marker (26) -->
    <a-marker type="barcode" value="26" id="marker-treasure" class="room-marker" data-room="room2">
      <a-entity id="treasure-entity" class="collectable-item" data-item="treasure" scale="0.5 0.5 0.5">
        <a-box position="0 0.3 0" width="1" height="0.6" depth="0.7" color="#7B4B2A"></a-box>
        <a-box position="0 0.65 0" width="1" height="0.1" depth="0.7" color="#5E3A20"></a-box>
        <a-cylinder position="0 0.5 0.35" radius="0.03" height="0.3" rotation="90 0 0" color="#FFD700"></a-cylinder>
        <a-sphere position="0 0.75 0" radius="0.07" color="#FFD700"></a-sphere>
      </a-entity>
    </a-marker>

    <a-entity camera></a-entity>
  </a-scene>

  <script src="game-logic.js"></script>
</body>
</html>
