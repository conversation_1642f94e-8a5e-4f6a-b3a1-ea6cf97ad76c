<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple AR Game</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
</head>
<body>
    <!-- Inventory Grid -->
    <div id="inventory">
        <div class="inventory-slot" id="slot-1"></div>
        <div class="inventory-slot" id="slot-2"></div>
        <div class="inventory-slot" id="slot-3"></div>
        <div class="inventory-slot" id="slot-4"></div>
    </div>

    <!-- Collect Button -->
    <button id="collectButton" style="display: none;">Collect</button>

    <!-- AR Scene -->
    <a-scene
        embedded
        arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 3x3; debugUIEnabled: false;"
    >
        <!-- Assets -->
        <a-assets>
            <!-- Default Hiro Marker Demo -->
            <a-asset-item id="cube-model" src="https://cdn.aframe.io/basic-guide/hello-world/models/BoxAnimated/BoxAnimated.gltf"></a-asset-item>
            
            <!-- Game Assets -->
            <a-asset-item
                id="key"
                png="png/key.svg"
                glb="glb/key.glb"
                collectable
                connect="door"
                marker="type='barcode' value='25'"
            ></a-asset-item>

            <a-asset-item
                id="door"
                src="assets/door.glb"
                marker="type='barcode' value='24'"
                animation="open"
                enable="room2"
            ></a-asset-item>

            <!-- Room 2 Assets (initially disabled) -->
            <a-asset-item
                id="treasure"
                png="png/treasure.svg"
                glb="glb/treasure.glb"
                collectable
                marker="type='barcode' value='26'"
                room="room2"
                disabled
            ></a-asset-item>
        </a-assets>

        <!-- Default Hiro Marker -->
        <a-marker preset="hiro">
            <a-entity
                gltf-model="#cube-model"
                scale="0.5 0.5 0.5"
                animation-mixer="loop: repeat"
            ></a-entity>
        </a-marker>

        <!-- Key Marker (Barcode 25) -->
        <a-marker type="barcode" value="25" id="marker-key">
            <a-entity
                id="key-entity"
                scale="0.3 0.3 0.3"
                class="collectable-item"
                data-item="key"
            >
                <!-- Simple key shape using primitives -->
                <a-cylinder position="0 0 0" radius="0.1" height="1" color="#FFD700"></a-cylinder>
                <a-box position="0 0.6 0" width="0.5" height="0.3" depth="0.1" color="#FFD700"></a-box>
                <a-box position="0.2 0.5 0" width="0.1" height="0.1" depth="0.1" color="#FFD700"></a-box>
                <a-box position="0.2 0.7 0" width="0.1" height="0.1" depth="0.1" color="#FFD700"></a-box>
            </a-entity>
        </a-marker>

        <!-- Door Marker (Barcode 24) -->
        <a-marker type="barcode" value="24" id="marker-door">
            <a-entity
                id="door-entity"
                scale="0.5 0.5 0.5"
                class="interactive-item"
                data-item="door"
                data-requires="key"
                data-animation="open"
                data-enable="room2"
            >
                <!-- Simple door shape -->
                <a-box position="0 1 0" width="1.5" height="2" depth="0.1" color="#8B4513"></a-box>
                <a-sphere position="0.6 1 0.1" radius="0.05" color="#FFD700"></a-sphere>
                <a-box position="0 2.2 0" width="1.5" height="0.4" depth="0.1" color="#654321"></a-box>
            </a-entity>
        </a-marker>

        <!-- Room 2 Treasure Marker (Barcode 26) - Initially disabled -->
        <a-marker type="barcode" value="26" id="marker-treasure" class="room-marker" data-room="room2" style="display: none;">
            <a-entity
                id="treasure-entity"
                scale="0.4 0.4 0.4"
                class="collectable-item"
                data-item="treasure"
            >
                <!-- Simple treasure chest -->
                <a-box position="0 0.3 0" width="1" height="0.6" depth="0.7" color="#8B4513"></a-box>
                <a-box position="0 0.7 0" width="1" height="0.2" depth="0.7" color="#654321"></a-box>
                <a-cylinder position="0 0.5 0.4" radius="0.05" height="0.3" color="#FFD700" rotation="90 0 0"></a-cylinder>
                <a-sphere position="0 0.8 0" radius="0.1" color="#FFD700"></a-sphere>
            </a-entity>
        </a-marker>

        <a-entity camera></a-entity>
    </a-scene>

    <script src="game-logic.js"></script>
</body>
</html>
