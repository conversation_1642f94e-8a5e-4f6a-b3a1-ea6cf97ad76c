#storyContainer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 1);
    z-index: 9999;
    padding: 20px;
  }


  @font-face { 
    font-family: pixel; 
    src: url('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/joystix%20monospace.otf?v=1715175944281'); 
  } 
     
  #storyText {
    font-family: pixel;
    color: #5d5f5c;
    font-size: 24px;
    text-align: center;
    margin: 0 10px;
  }

  #closeButton {
    color: #5d5f5c;
    font-family: pixel;
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 18px;
    padding: 10px 20px;
  }

/*  #buttonsContainer {
    margin-top: 20px;
    text-align: center;
    justify-content: center;
  }

  #button {
    color: #5d5f5c;
    font-family: pixel;
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 18px;
    padding: 10px 20px;
    margin: 0 10px;
  }
 */

#buttonsContainer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

button {
  font-family: pixel;
  font-size: 24px;
  padding: 15px 30px;
  margin: 0 20px;
  color: #5d5f5c;
  background-color: transparent;
  border: 2px solid #5d5f5c;
  cursor: pointer;
}

button:hover {
  background-color: #5d5f5c;
  color: #ffffff;
}

   .a-enter-vr-button {
    display: none;
  }

#imageContainer {
  opacity: 0.8;
  position: fixed;
  bottom: 20px; /* Adjust the distance from the bottom */
  left: 0;
  right: 0;
  text-align: center;
  z-index: 9999;
  pointer-events: none; /* Add this line to prevent interaction with the image */
}

#sceneImage {
  min-width: 50%;
  /* center image */
  height: auto;
  vertical-align: bottom; /* Add this line to align the image to the bottom */
}

#scanButtonContainer {
  position: fixed;
  bottom: 20px;
  left: 1;
  right: 0;
  text-align: center;
  z-index: 9999;
}

#scanButton {
  font-family: pixel;
  background-color: red;
  color: white;
  font-size: 24px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
}