// // Function to append messages to the log
// function appendToLog(message) {
//   const logElement = document.getElementById('log');
//   logElement.innerHTML += `<div>${message}</div>`; // Append new message
//   logElement.scrollTop = logElement.scrollHeight; // Auto-scroll to the bottom
// }

// // Modify your setupEventListeners function
// function setupEventListeners() {
//   Object.entries(actionMarkers).forEach(([markerName, markerInfo]) => {
//     if (!markerInfo.element) {
//       console.warn(`Element for marker '${markerName}' not found.`);
//       return;
//     }
    
//     markerInfo.element.addEventListener("markerFound", () => {
//       console.log(`${markerName} found`);
//       appendToLog(`${markerName} found`); // Update to append to log
//       markerInfo.visible = true;
//       handleMarkerInteraction(markerName);
//     });
    
//     markerInfo.element.addEventListener("markerLost", () => {
//       console.log(`${markerName} lost`);
//       appendToLog(`${markerName} lost`); // Update to append to log
//       markerInfo.visible = false;
//     });
//   });
// }
