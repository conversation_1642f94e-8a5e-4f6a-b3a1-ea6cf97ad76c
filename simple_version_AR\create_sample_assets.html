<!DOCTYPE html>
<html>
<head>
    <title>Create Sample Assets</title>
</head>
<body>
    <canvas id="canvas" width="64" height="64" style="border: 1px solid black;"></canvas>
    <br>
    <button onclick="createKeyImage()">Create Key PNG</button>
    <button onclick="createTreasureImage()">Create Treasure PNG</button>
    <br><br>
    <div id="downloads"></div>

    <script>
        function createKeyImage() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 64, 64);
            
            // Draw key
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(10, 25, 30, 8);
            ctx.fillRect(35, 20, 8, 18);
            ctx.fillRect(40, 22, 4, 4);
            ctx.fillRect(40, 30, 4, 4);
            
            // Download
            downloadCanvas('key.png');
        }
        
        function createTreasureImage() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 64, 64);
            
            // Draw treasure chest
            ctx.fillStyle = '#8B4513';
            ctx.fillRect(15, 30, 34, 20);
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(18, 33, 28, 14);
            ctx.fillStyle = '#FF6B6B';
            ctx.fillRect(30, 25, 4, 10);
            
            // Download
            downloadCanvas('treasure.png');
        }
        
        function downloadCanvas(filename) {
            const canvas = document.getElementById('canvas');
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            
            const div = document.getElementById('downloads');
            div.appendChild(link);
            link.textContent = `Download ${filename}`;
            div.appendChild(document.createElement('br'));
            
            link.click();
        }
    </script>
</body>
</html>
