@keyframes scaleIn {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes scaleOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

#storyContainer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 1);
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.5s ease-in-out;
}

#storyContainer.hide {
  animation: fadeOut 0.5s ease-in-out;
}

#storyText {
  font-size: 15px;
  font-family: pixel;
  text-align: center;
  margin-bottom: 20px;
  color: #5d5f5c;
  animation: scaleIn 0.5s ease-in-out;
  padding: 20px; /* Add padding to create spacing */
}

#storyText.hide {
  animation: scaleOut 0.5s ease-in-out;
}


  @font-face { 
    font-family: pixel; 
    src: url('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/joystix%20monospace.otf?v=1715175944281'); 
  } 
     


@media screen and (min-width: 768px) {
  #storyText {
    font-size: 20px;
  }
}

@media screen and (min-width: 1200px) {
  #storyText {
    font-size: 25px;
  }
}

  #closeButton {
    color: #5d5f5c;
    font-family: pixel;
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 18px;
    padding: 10px 20px;
  }


#buttonsContainer {
  scale: 60%; 
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media screen and (min-width: 768px) {
  #buttonsContainer {
  scale: 80%; 
  }
}

@media screen and (min-width: 1200px) {
  #buttonsContainer {
  scale: 100%; 
  }
}

button {
  font-family: pixel;
  font-size: 24px;
  padding: 15px 30px;
  margin: 0 20px;
  color: #5d5f5c;
  background-color: transparent;
  border: 2px solid #5d5f5c;
  cursor: pointer;
}

button:hover {
  background-color: #5d5f5c;
  color: #ffffff;
}

   .a-enter-vr-button {
    display: none;
  }


#imageContainer {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 9999;
  pointer-events: none;
}

#sceneImage {
  max-width: 100%;
  max-height: 20vh;
  min-width: 120px;
  min-height: 60px;
}

@media screen and (min-width: 768px) {
  #sceneImage {
    max-height: 15vh;
    min-width: 160px;
    min-height: 80px;
  }
}

@media screen and (min-width: 1200px) {
  #sceneImage {
    max-height: 10vh;
    min-width: 200px;
    min-height: 100px;
  }
}

#scanButtonContainer {
  position: fixed;
  scale: 80%; 
  bottom: 100px;
  left: 0; /* Changed from 1 to 0 */
  right: 0;
  text-align: center;
}

@media screen and (min-width: 768px) {
  #scanButtonContainer {
  scale: 90%; 
  bottom: 140px;
  }
}

@media screen and (min-width: 1200px) {
  #scanButtonContainer {
  scale: 100%; 
  bottom: 160px;
  }
}

#scanButton {
  font-family: pixel;
  background-color: red;
  color: white;
  font-size: 24px;
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  z-index: 0;
  transition: background-color 0.3s ease, transform 0.3s ease; /* Smooth transition for background and transform */
}

#scanButton:disabled {
  background-color: grey;
  cursor: not-allowed;
}

#scanButton:hover {
  background-color: darkred; /* Change to a darker shade of red */
  transform: scale(1.05); /* Slightly enlarge the button */
}

#timerContainer {
  position: fixed;
  top: 60px;
  right: 20px;
/*   z-index: 9999; */
  background-color: white;
  padding: 10px; /* Adjust padding to your liking */
  display: flex; /* Use flexbox for centering */
  justify-content: center; /* Center horizontally */
  align-items: center; /* Center vertically */
  max-height: 40px;
}

#timerText {
  font-family: pixel;
  font-size: 24px;
  color: red;
}

@media screen and (min-width: 768px) {
  #timerContainer {
  scale: 90%; 
  bottom: 140px;
  }
}

@media screen and (min-width: 1200px) {
  #timerContainer {
  scale: 100%; 
  bottom: 160px;
  }
}

@keyframes timeChange {
  0% { transform: scale(1); }
  50% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

.time-change {
  animation: timeChange 0.5s ease-in-out;
}
