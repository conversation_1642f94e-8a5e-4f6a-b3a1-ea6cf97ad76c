// Node.js script to generate simple PNG assets
const fs = require('fs');
const { createCanvas } = require('canvas');

function createKeyIcon() {
    const canvas = createCanvas(64, 64);
    const ctx = canvas.getContext('2d');
    
    // Clear background
    ctx.fillStyle = 'rgba(0,0,0,0)';
    ctx.fillRect(0, 0, 64, 64);
    
    // Draw key
    ctx.fillStyle = '#FFD700';
    ctx.fillRect(10, 28, 30, 8);
    ctx.fillRect(35, 20, 8, 18);
    ctx.fillRect(40, 22, 4, 4);
    ctx.fillRect(40, 30, 4, 4);
    
    // Add outline
    ctx.strokeStyle = '#B8860B';
    ctx.lineWidth = 1;
    ctx.strokeRect(10, 28, 30, 8);
    ctx.strokeRect(35, 20, 8, 18);
    
    return canvas.toBuffer('image/png');
}

function createTreasureIcon() {
    const canvas = createCanvas(64, 64);
    const ctx = canvas.getContext('2d');
    
    // Clear background
    ctx.fillStyle = 'rgba(0,0,0,0)';
    ctx.fillRect(0, 0, 64, 64);
    
    // Draw treasure chest
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(15, 35, 34, 20);
    
    // Chest lid
    ctx.fillStyle = '#654321';
    ctx.fillRect(15, 30, 34, 8);
    
    // Gold inside
    ctx.fillStyle = '#FFD700';
    ctx.fillRect(18, 38, 28, 14);
    
    // Lock
    ctx.fillStyle = '#C0C0C0';
    ctx.fillRect(30, 25, 4, 10);
    
    // Add outlines
    ctx.strokeStyle = '#654321';
    ctx.lineWidth = 1;
    ctx.strokeRect(15, 35, 34, 20);
    ctx.strokeRect(15, 30, 34, 8);
    
    return canvas.toBuffer('image/png');
}

// Create directories if they don't exist
if (!fs.existsSync('png')) {
    fs.mkdirSync('png');
}

// Generate and save icons
try {
    fs.writeFileSync('png/key.png', createKeyIcon());
    fs.writeFileSync('png/treasure.png', createTreasureIcon());
    console.log('PNG assets generated successfully!');
} catch (error) {
    console.error('Error generating assets:', error);
    console.log('Canvas module not available. Please create PNG files manually.');
}
