<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Barcode Marker Test</title>
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
</head>
<body style="margin: 0; overflow: hidden;">
    <a-scene
        embedded
        arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 3x3; debugUIEnabled: true;"
        vr-mode-ui="enabled: false;"
        renderer="logarithmicDepthBuffer: true;"
        device-orientation-permission-ui="enabled: false"
    >
        <!-- Test Hiro Marker -->
        <a-marker preset="hiro">
            <a-box position="0 0.5 0" material="color: red;"></a-box>
            <a-text value="HIRO" position="0 1 0" align="center"></a-text>
        </a-marker>

        <!-- Test Barcode 24 -->
        <a-marker type="barcode" value="24">
            <a-cylinder position="0 0.5 0" radius="0.3" height="1" material="color: blue;"></a-cylinder>
            <a-text value="BARCODE 24" position="0 1.5 0" align="center"></a-text>
        </a-marker>

        <!-- Test Barcode 25 -->
        <a-marker type="barcode" value="25">
            <a-sphere position="0 0.5 0" radius="0.5" material="color: green;"></a-sphere>
            <a-text value="BARCODE 25" position="0 1.5 0" align="center"></a-text>
        </a-marker>

        <!-- Test Barcode 26 -->
        <a-marker type="barcode" value="26">
            <a-cone position="0 0.5 0" radius-bottom="0.5" height="1" material="color: yellow;"></a-cone>
            <a-text value="BARCODE 26" position="0 1.5 0" align="center"></a-text>
        </a-marker>

        <a-entity camera></a-entity>
    </a-scene>

    <div style="position: fixed; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 10px; border-radius: 5px; font-family: Arial;">
        <h3>Barcode Test</h3>
        <p>Scan these markers:</p>
        <ul>
            <li>Hiro: Red Box</li>
            <li>Barcode 24: Blue Cylinder</li>
            <li>Barcode 25: Green Sphere</li>
            <li>Barcode 26: Yellow Cone</li>
        </ul>
        <p><small>Print barcode markers from AR.js generator</small></p>
    </div>
</body>
</html>
