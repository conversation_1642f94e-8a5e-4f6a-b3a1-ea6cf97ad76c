// Marker Definitions
var actionMarkers = {
  wallet: { element: document.querySelector("#wallet"), visible: false },
  coin: { element: document.querySelector("#coin"), visible: false },
  ball: { element: document.querySelector("#ball"), visible: false }, 
  boat: { element: document.querySelector("#boat"), visible: false },
  bush: { element: document.querySelector("#bush"), visible: false },
  shop: { element: document.querySelector("#shop"), visible: false },
  clock: { element: document.querySelector("#clock"), visible: false },
  chest: { element: document.querySelector("#chest"), visible: false },
  ticket: { element: document.querySelector("#ticket"), visible: false },
  bus: { element: document.querySelector("#bus"), visible: false },
  vm: { element: document.querySelector("#vm"), visible: false },
  bush1: { element: document.querySelector("#bush1"), visible: false },
  bush2: { element: document.querySelector("#bush2"), visible: false },
  bush3: { element: document.querySelector("#bush3"), visible: false },
  hg1: { element: document.querySelector("#hg1"), visible: false },
  hg2: { element: document.querySelector("#hg2"), visible: false },
  hg3: { element: document.querySelector("#hg3"), visible: false },
  hg4: { element: document.querySelector("#hg4"), visible: false },
  key1: { element: document.querySelector("#key1"), visible: false },
  key2: { element: document.querySelector("#key2"), visible: false },
  key3: { element: document.querySelector("#key3"), visible: false },
};


var currentScene = "scene0";
var subSceneIndex = 0;
var animationStates = {};

// Initialize the game
resetGame();


function tellStory(textParts, callback) {
  var storyContainer = document.getElementById('storyContainer');
  var storyText = document.getElementById('storyText');
  var buttonsContainer = document.getElementById('buttonsContainer');
  var backButton = document.getElementById('backButton');
  var nextButton = document.getElementById('nextButton');

  var currentPart = 0;

  function displayTextPart() {
    storyText.innerText = textParts[currentPart];
    backButton.style.display = currentPart > 0 ? 'inline-block' : 'none';
    nextButton.innerText = currentPart < textParts.length - 1 ? 'Tālāk' : 'Aizvērt';
  }

  function moveToNextPart() {
    if (currentPart < textParts.length - 1) {
      currentPart++;
      displayTextPart();
    } else {
      storyContainer.style.display = 'none';
      if (callback) {
        callback();
      }
      // Remove the event listeners after the story is completed
      backButton.removeEventListener('click', moveToPreviousPart);
      nextButton.removeEventListener('click', moveToNextPart);
    }
  }

  function moveToPreviousPart() {
    if (currentPart > 0) {
      currentPart--;
      displayTextPart();
    }
  }

  backButton.addEventListener('click', moveToPreviousPart);
  nextButton.addEventListener('click', moveToNextPart);

  storyContainer.style.display = 'flex';
  displayTextPart();
}

function updateSceneImage(imageSrc) {
  var sceneImage = document.getElementById('sceneImage');
  sceneImage.src = imageSrc;
}

function startGame() {
  currentScene = "scene0";
  subSceneIndex = 0;

  // Trigger the onStart function of scene0
  if (scenes[currentScene].onStart) {
    scenes[currentScene].onStart();
  }
}


function moveToNextSubscene() {
  const currentActions = scenes[currentScene].actions;

  if (subSceneIndex < currentActions.length - 1) {
    subSceneIndex++;
  } else {
    // If it's the last subscene, move to the next scene
    moveToNextScene();
  }
}

function moveToNextScene(nextScene) {
  currentScene = nextScene;
  subSceneIndex = 0;
}

// Event Listeners for Markers
Object.values(actionMarkers).forEach((marker) => {
  marker.element.addEventListener("markerFound", () => {
    marker.visible = true;
    handleMarkerInteraction(marker.element.id);
  });
  marker.element.addEventListener("markerLost", () => {
    marker.visible = false;
  });
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
  console.log(
    `Handling interaction for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
  );
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    console.log(`Executing action for marker: ${markerId}`);
    currentActions[markerId]();
  } else {
    console.log(
      `No action defined for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
    );
  }
}

var scenes = {
  scene0: {
    onStart: () => {
      tellStory(["Laipni lūdzam spēlē “Leciens uz priekšu”!",
                        "Tu atrodies Venēcijā un seko līdzi gumijas bumbiņai, ar telefona palīdzību atklājot marķierus, kas paslēpti zem durvīm. Izvēlies nepieciešamās kārtis, risini mīklas un atklāj noslēpumus katrā spēles vietā.",
        "Vai esi gatavs sākt piedzīvojumu?"
              ],  () => {
       // moveToNextScene("scene1");
        completeScene("scene1");
 // setEntityVisibility("shop-entity", true),
        // completeScene("shopScene");
      });
      updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079');

    },
  },
  scene1: {
    actions: [
        // Subscene 1
      {
        vm: () => {
          playAnimation("c", "vm");
          setEntityVisibility("wallet-entity", true);
            moveToNextScene("vendingMachineScene");
          setTimeout(() => {
          tellStory(["Mūs atstāja vienas! Ir trešā ceļojuma diena. Staigājot pa Venēcijas ielām, ar draudzeni palikām divatā, jo vecāki atgriezās atpakaļ kempingā.",
                     "Likās neticami pirmo reizi būt vienām svešā pilsētā, svešā valstī.",
                     "completeSceneKabatā bija palikušas pēdējās monētas. Kādā šaurā ieliņā pamanījām spēļu automātu, pie kura uzreiz pienācām un intereses pēc izlēmām to izmēģināt."
                     ], () => {
          });
            updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/2.png?v=1715180829756')
         }, 2000);
        },
      },
    ],
  },
  vendingMachineScene: {
    actions: [
      {
        // Subscene 1
        vm: () => {
          displayMessage("Tiec pie bumbas!")
        },
        wallet: () => {
          playAnimation("maks parādās", "wallet"),
          displayMessage("Paņem monētu"),
          setEntityVisibility("coin-entity", true),
          setTimeout(() => {
            moveToSubScene(1)
          }, 1500);
        },
        coin: () => displayMessage("Meklē citur"), // No action
        ball: () => displayMessage("Tev nepieciešama monēta") // No action
      },
      {
  // Subscene 2
        vm: () => {},
        wallet: () => {},
        coin: () => {
          playAnimation("monēta pazūd", "wallet"), // Play disappearing animation for wallet
          playAnimation("moneta paradas", "coin"), // Then play the coin appearing animation
          playAnimation("b", "vm"), // Ensure vending machine animation plays
          setEntityVisibility("ball-entity", true), // Make sure ball is visible if needed here or earlier
          moveToSubScene(2)
        },
        ball: () => displayMessage("Tev nepieciešama monēta") // No action
},

      
      {
  // Subscene 3

        ball: () => {
          playAnimation("moneta pazud", "coin"), // Coin disappearing
          playAnimation("bumba paradas", "ball"), // Ball appears
          playAnimation("a", "vm"), // Vending machine action
          playAnimation("maks pazūd", "wallet"),
          setEntityVisibility("boat-entity", true),// Wallet disappearing
            moveToNextScene("ballScene");
          setTimeout(() => {
          tellStory(["Lecoša gumijas bumbiņa? Tā bija tik muļķīga, ka mēs tai uzreiz pieķērāmies.",
            "Jau tuvojās vakars un attapāmies, ka drīz pienāks pēdējā laiva uz kempingu.",
            "Visu ceļu līdz laivai mētājām bumbiņu. Tā bija tik atsperīga, ka to vairākas reizes gandrīz pazaudējām. "
            ], () => {
          });
          updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/3.png?v=1715180830283')

          }, 1000);
        },
      },
    ],
  },
  ballScene: {
    actions: [
      {
        // Subscene 1
        ball: () => {
        displayMessage("Noliec bumbas kārti blakus laivas pieturai")
        },
        
        boat: () => {
        setEntityVisibility("wallet-entity", false),
        setEntityVisibility("coin-entity", false),
        setEntityVisibility("vm-entity", false),
        playAnimation("bumba pazud", "ball"),
        playAnimation("laiva paradas", "boat"),
           moveToNextScene("boatScene");
        setTimeout(() => {
           tellStory(["Beidzot var apsēsties!",
                      "Braucot laivā uznāca nogurums no staigāšanas, un atcerējos par garo nogurdinošo pargājienu mežā, kur paliku gulēt teltī.",
                      "Tas bija biedējoši…BUMBIŅA RIPO PROM! Gandrīz iekrita ūdeni!"
                     ], () => {
           });
          }, 10000);
        },
      },
    ],
  },
  boatScene: {
    actions: [
      {
        // Subscene 1
        boat: () => {
        displayMessage("Pārvieto laivu pa upi līdz mežam, kur atrodās kempings")
   
        },
        bush: () => {
         setEntityVisibility("bush-entity", true),
        setEntityVisibility("ball-entity", false),
        playAnimation("laiva pazud", "boat"),
        playAnimation("bumba paradas", "bush"),
            moveToNextScene("bushScene");
        setTimeout(() => {
            tellStory(["Naktis ir tik siltas! Kempingā ieradāmies ar tumsu. Bija jau divi naktī. Vecāki jau gulēja, bet mēs izlēmām iziet vakara pastaigā pa kempingu.",
                       "Turpat parkā mētājām bumbiņu pāri krūmam, pēkšņi tā atsitās pret draudzenes kroksi un ieripoja krūmā. Turpmāko 30 minūšu laika meklējām bumbiņu krūmos, kamēr pie mums nepienāca apsargs…",
                       "Uz brīdi likās, ka būs nepatikšanas, bet viņš mums palīdzēja meklējumos ar savu spilgto lukturīti."], () => {
            });
                     updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/4.png?v=1715180830636')
          }, 8000);        
        }
      },
    ],
  },
  bushScene: {
    actions: [
      {
        // Subscene 1
        bush: () => {
         displayMessage("Palīdzi bumbai atrast ceļu ārā no krūmiem")
        },
        bush1: () => {},
        bush2: () => {moveToSubScene(1,),
                    playAnimation("lec1", "bush")
      },
        bush3: () => {},
      },
      {
        // Subscene 2
        bush: () => {
         setEntityVisibility("boat-entity", false) //šo pārcēlu te, lai laiva varētu atspēlēt pazušanas animāciju iepriekšējā ainā
        },
        bush1: () => resetToSubScene(0),
        bush2: () => {},
        bush3: () => {moveToSubScene(2),
                     playAnimation("lec2", "bush")
      }
      },
      {
        // Subscene 3
        bush1: () => {
          playAnimation("bumba izlec", "bush"), 
          setEntityVisibility("shop-entity", true),
          displayMessage("Noliec bumbas kārti pie veikala")
          moveToNextScene("shopScene");
          setTimeout(() => {
              tellStory(["Atradās! Beidzot varējām iet gulēt, un apsargs – turpināt savu darbu.",
                         "Galvenais, rīt neaizmirst nopirkt suvenīrus…"],
               () => {
          });
      updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079');
          }, 6000);
      },
        bush2: () => resetToSubScene(0),
        bush3: () => {},
      },
    ],
  },
  
  shopScene: {
    actions: [
      {
        shop: () => {
           playAnimation("bumba ielec", "shop"),
           setEntityVisibility("bush-entity", false),
           setEntityVisibility("hg1-entity", true),
           setEntityVisibility("hg2-entity", true),
           setEntityVisibility("hg3-entity", true),
           setEntityVisibility("hg4-entity", true)
           // displayMessage("You have 20sec to analyse hourglasses"),
          setTimeout(() => {
            tellStory(["Pēdējā diena Venēcijā! Gājām pirkt suvenīrus. Tā man vienmēr ir vienmēr bijusi atbildīgākā ceļojuma daļa. Mājās gaida divas ģimenes un draugi, kuriem kaut kas jāatved no ceļojuma.",
                       "Ilgi staigājām pa veikalu un iepirkumu grozs palika smagāks un smagāks, līdz atcerējāmies, ka veikals kempingā veras ciet jau dienas vidū.",
                       "Palika trīs minūtes. Aizmirsu par suvenīru omei, un kur palika bumbiņa?!"], () => {
            });
             moveToNextScene("hgScene");
            updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/5%2C6.png?v=1715180830981')
          }, 10000);
        },
      },
    ],
  },
  hgScene: {
      actions: [
      {
        // Subscene 1
        hg1: () => {
              playAnimation("sp paradas", "hg1")
              },
        hg2: () => {
              playAnimation("sp paradas", "hg2")
               },
        hg3: () => {
              playAnimation("sp paradas", "hg3")
               },
        hg4: () => {
               playAnimation("sp paradas", "hg4"),
               moveToSubScene(1)
                }
      },
      {
        // Subscene 2
        shop: () => {
                      playAnimation("bumba izlec", "shop")    
        },

        hg1: () => {
                    playAnimation("sp stav", "hg1")
                    
      },
        hg2: () => {
                    playAnimation("sp stav", "hg2")
                    
      },
        hg3: () => {
                      playAnimation("sp stav", "hg3")
                    
      },
        hg4: () => {
                     playAnimation("sp stav", "hg4"),
                     displayMessage("Analyzing...")
                     setTimeout(() => {
                      moveToSubScene(2);  // Moves to subscene 3 after 20 seconds
                    }, 20000);  // 20000 milliseconds = 20 seconds
        }
      },
        {
        // Subscene 3
                  shop: () => {
              setEntityVisibility("shop-entity", false)
        },
        hg1: () => {
                    playAnimation("NlaTrack.001", "hg1")
                    
      },
        hg2: () => {
                    playAnimation("sp pazud", "hg2")
                    
      },
        hg3: () => {
                      playAnimation("sp pazud", "hg3")
                     
      },
        hg4: () => {
                     playAnimation("sp pazud", "hg4"),
                     moveToSubScene(3),
                     setEntityVisibility("clock-brockenA-entity", true),
                     displayMessage("Analyzing complete. Scan clock marker")
                     
                     
        }
      },
        // Subscene 4
      {
        clock: () => {
                     playAnimation("pulkstenis saplist", "clock"),
                     setEntityVisibility("hg1-entity", false),
                     setEntityVisibility("hg2-entity", false),
                     setEntityVisibility("hg3-entity", false),
                     setEntityVisibility("hg4-entity", false),
                    displayMessage("The correct time is __:__"),
       moveToNextScene("clockScene");

                     tellStory(["Vai pulkstenis ir saplīsis?",
                                "Bumbiņa bija izripojusi ārā no veikala uz lielā pulksteņa pusi. Apsēdāmies uz soliņa apskatīt nopirkto un parunāt.",
                                "Pēc kāda laika apskatījām arī lielo pulksteni un bijām apmulsumā, kādēļ tas stāv un neiet uz priekšu. Telefoni mums bija izlādējušies, tādēļ cerējām, ka lielā pulkstena laiks nav pareizs, jo tas nozīmētu, ka mēs kavējam autobusu…"
                                ], () => {
      });
                     
      },
      },
    ],
  },

  clockScene: {
    actions: [
      {
        // Subscene 1
        hg1: () => {},
        hg2: () => {},
        hg3: () => {moveToSubScene(1),
                     displayMessage("The correct time is 1_:__"),
                     setEntityVisibility("clock-brocken-entity", true),
                     setEntityVisibility("clock-brockenA-entity", false)
                     },
        hg4: () => {},
      },
      {
        // Subscene 2
        hg1: () => {},
        hg2: () => {moveToSubScene(2),
                    displayMessage("The correct time is 16:__"),
                    setEntityVisibility("clock-brocken-entity", false),
                    setEntityVisibility("clock-handle1-entity", true)
                   },
        hg3: () => {},
        hg4: () => {},
      },
      {
        // Subscene 3
        hg1: () => {moveToSubScene(3),
                    displayMessage("The correct time is 16:5_"),
                    setEntityVisibility("clock-handle1-entity", false),
                    setEntityVisibility("clock-handle2-entity", true)
                   }, 
        hg2: () => {},
        hg3: () => {},
        hg4: () => {},
      },
      {
        // Subscene 4
        // Assuming you need an action here. Replace "nextScene" with the correct scene ID and ensure functions exist
        hg1: () => {},
        hg2: () => {},
        hg3: () => {},
        hg4: () => {setEntityVisibility("clock-handle2-entity", false),
                     setEntityVisibility("clock-ticking-entity", true),
                     displayMessage("Congrats! You fixed the clock. The correct time is 16:57"),
                     playAnimation("pulkstenis iet", "clock"),
                     setEntityVisibility("chest-entity", true),
       moveToNextScene("chestzeroScene");

                    tellStory(["Pēc ilgas sēdēšanas uz soliņa izdomājām tomēr doties mājās, ja nu tiešām ir tik vēls cik rādīja pulkstenī, jāpaspēj sakrāmēt vēl čemodāni. "], () => {
      });
      },
      },
      ],
  },
  chestzeroScene: {
    actions: [
      {
        // Subscene 5
       chest:() => {setEntityVisibility("clock-ticking-entity", false),
                   setEntityVisibility("chest-entity", true),
                   playAnimation("lade paradas", "chest"),
       moveToNextScene("chestScene");
                 tellStory(["Mēs aizmirsām kodu! Atnākot atpakaļ sapratām, ka mēs tik tiešām kavējam autobusu un vecāki jau aizbrauca! Ātri sakrāmējām somas.",
                            "Elektroniskās biļetes mums nebija pieejamas, jo telefons bija izlādējies. Papīra biļetes laikam atradās čemodānā, čemodānā ar kodu…Kāds bija kods un kur atkal palika bumbiņa??"], () => {
      });
                             updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/7.png?v=1715180831359')
      },
                   
                   
      },
    ],
  },
  chestScene: {
    actions: [
      {
        chest:() => {setEntityVisibility("chest-entity", true),
                     setEntityVisibility("key1-entity", true),
                     setEntityVisibility("key2-entity", true),
                     setEntityVisibility("key3-entity", true),
                     playAnimation("atslegaF paradas", "key1"),
                     playAnimation("atslegaF paradas", "key2"),
                     playAnimation("atslegaF paradas", "key3"),
                     playAnimation("lade stav", "chest"),
                     displayMessage("Connect key fragments in right order"),
                     moveToSubScene(1)
      },
      },
      {
        // Subscene 2
        key1: () => {moveToSubScene(2),
                    setEntityVisibility("key1-entity", true)},
        key2: () => {},
        key3: () => {},
      },
      {
        // Subscene 3
        key1: () => {},
        key2: () => {moveToSubScene(3),
                    setEntityVisibility("key2-entity", true)
                   },
        key3: () => resetToSubScene(0),
      },
      {
        // Subscene 4
        key1: () => resetToSubScene(0),
        key2: () => {},
        key3: () => {moveToSubScene(4),
                    setEntityVisibility("key3-entity", true),
                    setEntityVisibility("chest-entity", true),
                    playAnimation("lade atverta", "chest"),
                    setEntityVisibility("ticket-entity", true)
      },
      },
      {
        // Subscene 5
        ticket: () => {setEntityVisibility("key1-entity", false),
                      setEntityVisibility("key2-entity", false),
                      setEntityVisibility("key3-entity", false),
                      setEntityVisibility("bus-entity", true),
                      playAnimation("bilete paradas", "ticket"),
                      playAnimation("lade pazud", "chest"),
                   moveToNextScene("buszeroScene");  
                       tellStory(["Pārmeklējām visas pārējās somas ar cerību, ka biļetes nav čemodānā ar kodu.",
                                  "Vienā no somām atradām bumbiņu, kuru netīšām iemetām kopā ar visām mantām, bet tomēr biļetes atradās čemodānā ar kodu.",
                                  "Beigās dabujām vaļā čemodānu un atradām biļetes, bet bija jātiek vēl līdz autobusam, mums bija trīs opcijas, braukt ar skūteri, iet ar kājām vai izsaukt taksi."], () => {
      });
                      },
      },
    ],
  },
  buszeroScene: {
    actions: [
      {
        // Subscene 1
        bus: () => {playAnimation("piebrauc", "bus"),
                    setEntityVisibility("chest-entity", false),
                    playAnimation("bilete pazud", "ticket"),
       moveToNextScene("busScene");

                    tellStory(["Mēs paspējām! Tikām līdz autobusam, kur mūs sagaidīja saraizējušies vecāki. Iekāpām autobusā un sākām ceļu uz lidostas pusi."], () => {
      });
                   },
                    
      },
      ],
  },
  busScene: {
      actions:[
      {
        // Subscene 1
        bus: () => {playAnimation("stav", "bus"),
                    setEntityVisibility("ticket-entity", false),
                    moveToSubScene(1)
                },
      },
      {
        // Subscene 2
        bus: () => {playAnimation("aizbrauc", "bus"),
       moveToNextScene("completeScene");

                    tellStory(["Tā arī pienāca ceļojuma beigas. Ceļojums bija piedzīvojumu, prieka un izaicinājumu pilns un daļa no tā bija lecošā gumijas bumbiņa.",
                               " Neskatoties uz neskaitāmām reizēm, kad bumbiņa pazuda vai aizripoja, vienmēr atradās risinājums un motivācija to atrast.",
                               "Tāpat, kā spēles autores dzīvē, īstermiņa atmiņas problēmas bieži vien ietekmē ikdienas gaidu un sarežģī to. Meklējot sevi un risinot problēmas vienmēr atradīsies cilvēki, kas palīdzēs, atradīsies iekšējā motivācija kaut ko mainīt un cīnīties par labāko.",
                               " “Tas vienmēr šķiet neiespējami, kamēr tas nav izdarīts”"], () => {
      });
                  
                },
      },
        ],
  },
  completeScene: {
        actions: [
      {
       complete: () => {}
      
      },
    ],
  },

  // Ensure other scenes are correctly defined...
};


const animationEntities = [
  document.querySelector("#vm-entity"),
  document.querySelector("#wallet-entity"),
  document.querySelector("#coin-entity"),
  document.querySelector("#ball-entity"),
  document.querySelector("#boat-entity"),
  document.querySelector("#bush-entity"),
  document.querySelector("#shop-entity"),
  document.querySelector("#clock-brockenA-entity"),
  document.querySelector("#clock-brocken-entity"),
  document.querySelector("#clock-handle1-entity"),
  document.querySelector("#clock-handle2-entity"),
  document.querySelector("#clock-ticking-entity"),
  document.querySelector("#chest-entity"),
  document.querySelector("#ticket-entity"),
  document.querySelector("#bus-entity"),
  document.querySelector("#hg1-entity"),
  document.querySelector("#hg2-entity"),
  document.querySelector("#hg3-entity"),
  document.querySelector("#hg4-entity"),
  document.querySelector("#key1-entity"),
  document.querySelector("#key2-entity"),
  document.querySelector("#key3-entity"),
];

function playAnimation(animationName, markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity) {
    // Remove any existing animation-mixer component
    entity.removeAttribute('animation-mixer');

    // Add the animation-mixer component with the specified animation clip
    entity.setAttribute('animation-mixer', {
      clip: animationName,
      loop: 'once',
      clampWhenFinished: true
    });
  }
}

function playMarkerAnimation(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const animationName = getAnimationName(markerId);
    if (animationName) {
      playAnimation(animationName, markerId);
    }
  }
}

function resetMarkerAnimation(markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity && entity.getAttribute("animation-mixer")) {
    entity.removeAttribute("animation-mixer");
  }
}

function getAnimationName(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const actionFunc = currentActions[markerId];
    const actionString = actionFunc.toString();
    const match = actionString.match(/playAnimation\(["'](.+)["']/);
    if (match) {
      return match[1];
    }
  }
  return null;
}

//------------------------------------------------------------------
//  Funkcijas
//------------------------------------------------------------------


function resetAnimationStates() {
  Object.keys(animationStates).forEach((key) => (animationStates[key] = false));
}

function resetAnimationState(markerId) {
  const animationKey = `${currentScene}_${subSceneIndex}_${markerId}`;
  animationStates[animationKey] = false;
}

function setupEventListeners() {
  Object.values(actionMarkers).forEach((marker) => {
    marker.element.addEventListener("markerFound", () => {
      marker.visible = true;
      handleMarkerInteraction(marker.element.id);
    });    
    marker.element.addEventListener("markerLost", () => {
      marker.visible = false;
    });
  });
}


function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;
}

function completeScene(nextScene, ...actions) {
  console.log(`Completing scene: ${currentScene}, moving to: ${nextScene}`);
  currentScene = nextScene;
  subSceneIndex = 0; // Reset subSceneIndex for the new scene
  actions.forEach((action) => action());
}

function displayMessage(text) {
  const messageElement = document.getElementById("message");
  if (messageElement) {
    messageElement.innerText = text;
    messageElement.style.display = "block"; // Make sure it's visible

    setTimeout(() => {
      messageElement.style.display = "none";
    }, 4000); // 2000 milliseconds = 2 seconds
  } else {
    console.error("Message element not found");
  }
}

function setEntityVisibility(entityId, visible) {
  const entity = document.querySelector(`#${entityId}`);
  if (entity) {
    entity.setAttribute("visible", visible);
  } else {
    console.error(`Element #${entityId} not found`);
  }
}

startGame();

function resetScene() {
  subSceneIndex = 0;
  // resetBushPosition();
  // resetClockPosition();
}


function resetGame() {
  // resetBushPosition();
  // resetClockPosition();

  setEntityVisibility("coin-entity", false);
  setEntityVisibility("ball-entity", false); 
  setEntityVisibility("boat-entity", false);
  setEntityVisibility("bush-entity", false); 
  setEntityVisibility("shop-entity", false);
  setEntityVisibility("clock-brockenA-entity", false);
  setEntityVisibility("clock-brocken-entity", false);
  setEntityVisibility("clock-handle1-entity", false);
  setEntityVisibility("clock-handle2-entity", false);
  setEntityVisibility("clock-ticking-entity", false);
  setEntityVisibility("chest-entity", false);
  setEntityVisibility("ticket-entity", false);   
  setEntityVisibility("bus-entity", false);   
  setEntityVisibility("wallet-entity", false);   
  setEntityVisibility("vm-entity", true);
  setEntityVisibility("hg1-entity", false);
  setEntityVisibility("hg2-entity", false);
  setEntityVisibility("hg3-entity", false);
  setEntityVisibility("hg4-entity", false);
  setEntityVisibility("key1-entity", false);
  setEntityVisibility("key2-entity", false);
  setEntityVisibility("key3-entity", false);
  currentScene = "scene0";
  subSceneIndex = 0;
}


AFRAME.registerComponent("continuous-tracking", {
  tick: function () {
  },
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
