<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>AR Game</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="styles.css">
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
  </head>
  <body style="margin: 0; overflow: hidden">
    <p>
<!--     <div id="sceneInfo" style="position: absolute; top: 0; left: 0; color: white; background: rgba(0, 0, 0, 0.5);padding: 10px; z-index: 100;">
      Scene Info: <span id="currentScene">None</span> | Subscene:
      <span id="currentSubscene">None</span>
    </div>
    <div id="markersInfo" style=" position: absolute; top: 0; right: 0; color: white; background: rgba(0, 0, 0, 0.5); padding: 10px; z-index: 100;">
      Markers Info: <span id="currentMarker">None</span>
    </div>
    <div id="log" style="position: absolute; bottom: 0; right: 0; color: white; background: rgba(0, 0, 0, 0.5);padding: 10px; z-index: 100;">
    </div> -->
    <b>
      <div
        id="message"
        style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
          color: red; font-family: Arial; padding: 20px; z-index: 100; font-size: 2em;">
        Message goes here
      </div>
    </b>
</p>

      <a-scene
        embedded
        arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 3x3; debugUIEnabled: false;"
      >
      
      <!-- Nosaukti un ieladeti visi aseti -->
      <a-assets>
<!--         <a-asset-item id="vending-machine-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/VM-animation.glb?v=1713215286413"></a-asset-item> -->
        <a-asset-item id="vm-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/VM-animation.glb?v=1713215286413"></a-asset-item>
        <a-asset-item id="wallet-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Wallet-animation.glb?v=1713977053842"></a-asset-item>
        <a-asset-item id="coin-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Coin-animation.glb?v=1713976934952"></a-asset-item>
        <a-asset-item id="ball-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Ball-animation.glb?v=1713215288182"></a-asset-item>
        <a-asset-item id="boat-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Boat-animation.glb?v=1713215548185"></a-asset-item>
        <a-asset-item id="bush-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Bush-animations.glb?v=1713215290230"></a-asset-item>
        <a-asset-item id="shop-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Shop-animation.glb?v=1713215285172"></a-asset-item>
        <a-asset-item id="hourglass1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-1-animation.glb?v=1713215282063"></a-asset-item>
        <a-asset-item id="hourglass5-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-5-animation.glb?v=1713215283064"></a-asset-item>
        <a-asset-item id="hourglass6-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-6-animation.glb?v=1713215284083"></a-asset-item>
        <a-asset-item id="hourglass7-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-7-animation.glb?v=1713215284622"></a-asset-item>
        <a-asset-item id="clock-handle1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_1handle.glb?v=1713216063992"></a-asset-item>
        <a-asset-item id="clock-handle2-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_2handles.glb?v=1713216062245"></a-asset-item>
        <a-asset-item id="clock-ticking-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_ticking-animation.glb?v=1713960381821"></a-asset-item>
        <a-asset-item id="clock-brocken-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_brocken.glb?v=1713216063511"></a-asset-item>
        <a-asset-item id="clock-brockenA-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock-brocken-animation.glb?v=1713215292582"></a-asset-item>
        <a-asset-item id="chest-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Chest-animation.glb?v=1713216521118"></a-asset-item>
        <a-asset-item id="ticket-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Ticket-animation.glb?v=1713215285722"></a-asset-item>
        <a-asset-item id="key1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF01-animation.glb?v=1713716051759"></a-asset-item>
        <a-asset-item id="key2-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF02-animation.glb?v=1713716050624"></a-asset-item>
        <a-asset-item id="key3-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF03-animation.glb?v=1713716051011"></a-asset-item>
        <a-asset-item id="bus-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Bus-animation.glb?v=1713215289721"></a-asset-item>
      </a-assets>

      <!-- Items -->
      <a-marker
        id="wallet"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-pocket_icon.patt?v=1707915625462"
        preset="custom"
      >
        <a-entity
          id="wallet-entity"
          gltf-model="#wallet-model"
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="coin"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-coin_icon.patt?v=1707915624303"
        preset="custom"
      >
       <a-entity
          id="coin-entity" gltf-model="#coin-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="ball"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-ball_icon.patt?v=1707915628669"
        preset="custom"
      >
        <a-entity
          id="ball-entity" rotation= "0 90 0" scale=".5 .5 .5" gltf-model="#ball-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="one"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-one_icon.patt?v=1707915625076"
      >
        <a-entity
          id="hourglass1-entity" scale=".5 .5 .5" gltf-model="#hourglass1-model" animation-mixer="loop: repeat"
        ></a-entity>
        <a-entity
          id="key1-entity" gltf-model="#key1-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="two"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-two_icon.patt?v=1707915628292"
        preset="custom"
      >
        <a-entity
          id="hourglass5-entity" scale=".5 .5 .5" gltf-model="#hourglass5-model" animation-mixer="loop: repeat"
        ></a-entity>
        <a-entity
          id="key2-entity" gltf-model="#key2-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      <a-marker
        id="three"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-three_icon.patt?v=1707915626275"
      >
        <a-entity
          id="hourglass6-entity" scale=".5 .5 .5" gltf-model="#hourglass6-model" animation-mixer="loop: repeat"
        ></a-entity>
        <a-entity
          id="key3-entity" gltf-model="#key3-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      <a-marker
        id="four"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-four_icon.patt?v=1707915624683"
      >
        <a-entity
          id="hourglass7-entity" scale=".5 .5 .5" gltf-model="#hourglass7-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="ticket"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-ticket_icon.patt?v=1707915626629"
      >
        <a-entity
          id="ticket-entity" gltf-model="#ticket-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>

      
            <a-marker
        id="vm"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-vending_machine_icon.patt?v=1707914772104"
        preset="custom"
      >
        <a-entity
          id="vm-entity"
          gltf-model="#vm-model"
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>

      
      <a-marker
        id="boat"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-boat_icon.patt?v=1707915629116"
        preset="custom"
      > 
        <a-entity
          id="boat-entity"
          gltf-model="#boat-model"
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
     <a-marker
        id="bush"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-bush_icon.patt?v=1707915629845"
        preset="custom"
      >
        <a-entity
          id="bush-entity" scale=".25 .25 .25" gltf-model="#bush-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="shop"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-shop_icon.patt?v=1707915625854"
        preset="custom"
      >
        <a-entity
          id="shop-entity" gltf-model="#shop-model" scale="0.15 .15 0.15" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
            <a-marker
        id="clock"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-clock_icon.patt?v=1707915623930"
        preset="custom"
      >
        <a-entity
          id="clock-brocken-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-brocken-model" animation-mixer="loop: repeat"
        ></a-entity>
              
         <a-entity
          id="clock-brockenA-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-brockenA-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-handle1-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-handle1-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-handle2-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-handle2-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-ticking-entity" scale=".5 .5 .5" rotation="0 -90 0" position="3 0 0" gltf-model="#clock-ticking-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="chest"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-chest_icon.patt?v=1707915623505"
        preset="custom"
      >
        <a-entity
          id="chest-entity" scale=".5 .5 .5" rotation="0 180 0" gltf-model="#chest-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="bus"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-bus_icon.patt?v=1707915629486"
        preset="custom"
                >
        <a-entity
          id="bus-entity"  scale=".25 .25 .25" gltf-model="#bus-model" animation-mixer="loop: repeat"
        ></a-entity>     
      </a-marker>


      <a-entity camera></a-entity>
    </a-scene>
  
    <div id="storyContainer">
       <div id="storyText"></div>
       <button id="closeButton">Close</button>
    </div>
  
    <script src="markers.js"></script>
<!--     <script src="scene_finder.js"></script>
    <script src="marker-finder.js"></script> -->

  </body>
</html>
