// Marker Definitions
var actionMarkers = {
  marker1: { element: document.querySelector("#marker1"), visible: false },
  marker2: { element: document.querySelector("#marker2"), visible: false },
  marker3: { element: document.querySelector("#marker3"), visible: false },
  kubsMarker: {
    element: document.querySelector("#kubs-marker"),
    visible: false,
  },
};

var currentScene = "cylinder";
var subSceneIndex = 0;

// Initialize the game
resetGame();

// Event Listeners for Markers
Object.values(actionMarkers).forEach((marker) => {
  marker.element.addEventListener("markerFound", () => {
    marker.visible = true;
    handleMarkerInteraction(marker.element.id);
  });
  marker.element.addEventListener("markerLost", () => {
    marker.visible = false;
  });
});

// Add a console log to verify detection
actionMarkers.kubsMarker.element.addEventListener("markerFound", () => {
  console.log("kubs-marker found");
  actionMarkers.kubsMarker.visible = true;
  handleMarkerInteraction("kubs-marker");
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
  let currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    currentActions[markerId]();
  } else {
    console.log(
      `No action defined for ${markerId} in scene ${currentScene}, subscene ${subSceneIndex}`
    );
  }
}

function resetCurrentSceneToInitialSubscene() {
  // Reset all relevant objects and state for the current scene
  resetCylinderPosition(); // Reset the cylinder to its initial position
  resetCubePosition();
  resetConePosition(); // Add any additional reset logic here for other objects or state variables

  subSceneIndex = 0; // Reset to the first subscene

  // If you have any visible/invisible toggles based on subscenes, reset those here
  // For example:
  // document.querySelector("#cilindrs").setAttribute("visible", true);
  // Adjust visibility as needed for your scene setup

  console.log("Scene reset to initial subscene.");
}

var scenes = {
  cylinder: {
    actions: [
      {
        // Subscene 1 (skapī ir dēlis)
        // Ensure that this function is correctly referenced in your scenes object
        marker1: () => moveToSubScene(1, atverasSkapis),
        marker2: () => {}, // No action
        marker3: () => {}, // No action
      },
      {
        // Subscene 2 (skrituļdēlis)
        marker1: () => {}, // No action
        marker2: () =>
          completeScene("skrituldelis", showSkrituldelis, hideSkapis),
        marker3: () => resetCurrentSceneToInitialSubscene(),
      },
    ],
  },
  skrituldelis: {
    actions: [
      {
        marker1: () => {},
        "kubs-marker": () => completeScene("cube", hideSkrituldelis, showCube),
        marker3: () => {},
      },
    ],
  },

  cube: {
    actions: [
      {
        // Subscene 1
        marker2: () => moveToSubScene(1, moveCubeX),
        marker1: () => {}, // No action
        marker3: () => {}, // No action
      },
      {
        // Subscene 2
        marker2: () => {}, // No action
        marker1: () => moveToSubScene(2, moveCubeZ),
        marker3: () => resetCurrentSceneToInitialSubscene(),
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => {}, // No action
        marker3: () => completeScene("cone", hideCube, showCone),
      },
    ],
  },
  cone: {
    actions: [
      {
        // Subscene 1
        marker2: () => {}, // No action,
        marker1: () => {}, // No action
        marker3: () => moveToSubScene(1, moveConeX),
      },
      {
        // Subscene 2
        marker2: () => moveToSubScene(2, moveConeZ),
        marker1: () => resetCurrentSceneToInitialSubscene(),
        marker3: () => {}, // No action
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => completeScene("cone", hideCone),
        marker3: () => {}, // No action
      },
    ],
  },
};

function animateMove(object, targetPosition, duration) {
  let startTime = null;
  const startPosition = object.getAttribute("position");

  function animate(time) {
    if (!startTime) startTime = time;
    const elapsedTime = time - startTime;
    const fraction = elapsedTime / duration;

    if (fraction < 1) {
      let newX =
        startPosition.x + (targetPosition.x - startPosition.x) * fraction;
      object.setAttribute("position", {
        x: newX,
        y: startPosition.y,
        z: startPosition.z,
      });
      requestAnimationFrame(animate);
    } else {
      // Ensure the final position is set
      object.setAttribute("position", targetPosition);
    }
  }

  requestAnimationFrame(animate);
}

function atverasSkapis() {
  let cylinder = document.querySelector("#cilindrs");
  let currentPosition = cylinder.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  console.log("Starting animation for cylinder"); // Debugging log
  animateMove(cylinder, targetPosition, 1000); // 1000 ms for the animation
}

function showSkrituldelis() {
  let skrituldelis = document.querySelector("#skrituldelis");
  if (skrituldelis) {
    skrituldelis.setAttribute("visible", true);
  } else {
    console.error("Element #skrituldelis not found");
  }
}

function moveCylinderZ() {
  let cylinder = document.querySelector("#cilindrs");
  let currentPosition = cylinder.getAttribute("position");
  cylinder.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}

function moveCubeX() {
  let cube = document.querySelector("#kubs");
  let currentPosition = cube.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  animateMove(cube, targetPosition, 1000); // 1000 ms for the animation
}

function moveCubeZ() {
  let cube = document.querySelector("#kubs");
  let currentPosition = cube.getAttribute("position");
  cube.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveConeX() {
  let cone = document.querySelector("#konuss");
  let currentPosition = cone.getAttribute("position");
  let targetPosition = {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  };
  animateMove(cone, targetPosition, 1000); // 1000 ms for the animation
}

function moveConeZ() {
  let cone = document.querySelector("#konuss");
  let currentPosition = cone.getAttribute("position");
  cone.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;
  resetCylinderPosition(); // This will reset the position
  resetCubePosition();
  resetConePosition();
}

function resetCylinderPosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#cilindrs")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function resetCubePosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#kubs")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function resetConePosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#konuss")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function completeScene(nextScene, ...actions) {
  currentScene = nextScene;
  subSceneIndex = 0;
  actions.forEach((action) => action());
}
function hideSkapis() {
  let cylinder = document.querySelector("#cilindrs");
  if (cylinder) {
    cylinder.setAttribute("visible", false);
  } else {
    console.error("Cylinder #cilindrs not found");
  }
}

function hideSkrituldelis() {
  let skrituldelis = document.querySelector("#skrituldelis");
  if (skrituldelis) {
    skrituldelis.setAttribute("visible", false);
  } else {
    console.error("Element #skrituldelis not found");
  }
}

function hideCube() {
  let kubs = document.querySelector("#kubs");
  if (kubs) {
    kubs.setAttribute("visible", false);
  } else {
    console.error("Element #kubs not found");
  }
}

function showCube() {
  document.querySelector("#kubs").setAttribute("visible", true);
}
function hideCone() {
  document.querySelector("#konuss").setAttribute("visible", false);
}

function showCone() {
  document.querySelector("#konuss").setAttribute("visible", true);
}
function resetScene() {
  subSceneIndex = 0;
  resetCylinderPosition();
  resetCubePosition();
  resetConePosition();
  // Reset other objects if necessary
}

function resetGame() {
  document.querySelector("#kubs").setAttribute("visible", false); // Explicitly hide the cube
  document.querySelector("#konuss").setAttribute("visible", false);
  document.querySelector("#cilindrs").setAttribute("visible", true); // Show cylinder initially
  document.querySelector("#skrituldelis").setAttribute("visible", false); // Hide skrituldelis initially
  resetCylinderPosition();
  resetCubePosition();
  resetConePosition();

  // Reset the scene and subscene indexes
  currentScene = "cylinder";
  subSceneIndex = 0;

  // Reset any other game states as needed
}

// A-Frame Component for Continuous Tracking
AFRAME.registerComponent("continuous-tracking", {
  tick: function () {
    // Optional: Continuous tracking logic
  },
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
