// Simple AR Game Logic
class SimpleARGame {
    constructor() {
        this.inventory = [];
        this.enabledRooms = ['default']; // Start with default room enabled
        this.currentCollectable = null;
        this.collectButton = document.getElementById('collectButton');
        this.inventorySlots = document.querySelectorAll('.inventory-slot');
        
        this.init();
    }

    init() {
        console.log('Initializing Simple AR Game...');
        
        // Wait for A-Frame to be ready
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
            this.setupMarkerEvents();
            this.setupDragAndDrop();
        });
    }

    setupEventListeners() {
        // Collect button
        this.collectButton.addEventListener('click', () => {
            this.collectItem();
        });
    }

    setupMarkerEvents() {
        // Get all markers
        const markers = document.querySelectorAll('a-marker');
        
        markers.forEach(marker => {
            marker.addEventListener('markerFound', (e) => {
                this.onMarkerFound(e.target);
            });
            
            marker.addEventListener('markerLost', (e) => {
                this.onMarkerLost(e.target);
            });
        });
    }

    onMarkerFound(marker) {
        console.log('Marker found:', marker.id);
        
        // Check if this marker belongs to an enabled room
        const roomData = marker.getAttribute('data-room');
        if (roomData && !this.enabledRooms.includes(roomData)) {
            console.log('Room not enabled:', roomData);
            return;
        }
        
        // Check for collectable items
        const collectableEntity = marker.querySelector('.collectable-item');
        if (collectableEntity) {
            const itemId = collectableEntity.getAttribute('data-item');
            
            // Check if item is already collected
            if (!this.inventory.includes(itemId)) {
                this.currentCollectable = {
                    itemId: itemId,
                    entity: collectableEntity,
                    marker: marker
                };
                this.showCollectButton();
            }
        }
        
        // Check for interactive items (like doors)
        const interactiveEntity = marker.querySelector('.interactive-item');
        if (interactiveEntity) {
            this.setupDropZone(interactiveEntity);
        }
    }

    onMarkerLost(marker) {
        console.log('Marker lost:', marker.id);
        this.hideCollectButton();
        this.currentCollectable = null;
        
        // Remove drop zones
        const dropZones = marker.querySelectorAll('.drop-zone');
        dropZones.forEach(zone => zone.remove());
    }

    showCollectButton() {
        this.collectButton.style.display = 'block';
    }

    hideCollectButton() {
        this.collectButton.style.display = 'none';
    }

    collectItem() {
        if (!this.currentCollectable) return;
        
        const { itemId, entity, marker } = this.currentCollectable;
        
        // Add to inventory
        this.inventory.push(itemId);
        
        // Hide the 3D model
        entity.setAttribute('visible', false);
        
        // Add PNG to inventory grid
        this.addToInventoryGrid(itemId);
        
        // Hide collect button
        this.hideCollectButton();
        
        // Show success message
        this.showMessage(`Collected ${itemId}!`, 'success');
        
        console.log('Item collected:', itemId);
        console.log('Current inventory:', this.inventory);
    }

    addToInventoryGrid(itemId) {
        // Find first empty slot
        const emptySlot = Array.from(this.inventorySlots).find(slot =>
            !slot.classList.contains('filled')
        );

        if (emptySlot) {
            const img = document.createElement('img');
            // Try SVG first, fallback to PNG
            img.src = `png/${itemId}.svg`;
            img.alt = itemId;
            img.draggable = true;
            img.setAttribute('data-item', itemId);

            // Fallback to PNG if SVG fails
            img.onerror = () => {
                img.src = `png/${itemId}.png`;
            };

            emptySlot.appendChild(img);
            emptySlot.classList.add('filled');
            emptySlot.setAttribute('data-item', itemId);
        }
    }

    setupDropZone(entity) {
        const requiredItem = entity.getAttribute('data-requires');
        if (!requiredItem) return;
        
        // Create drop zone
        const dropZone = document.createElement('div');
        dropZone.className = 'drop-zone';
        dropZone.setAttribute('data-requires', requiredItem);
        
        // Add to the marker element (not the entity)
        const marker = entity.closest('a-marker');
        if (marker) {
            marker.appendChild(dropZone);
        }
    }

    setupDragAndDrop() {
        // Setup drag events for inventory items
        document.addEventListener('dragstart', (e) => {
            if (e.target.tagName === 'IMG' && e.target.hasAttribute('data-item')) {
                e.dataTransfer.setData('text/plain', e.target.getAttribute('data-item'));
                e.target.closest('.inventory-slot').classList.add('dragging');
            }
        });

        document.addEventListener('dragend', (e) => {
            if (e.target.tagName === 'IMG') {
                e.target.closest('.inventory-slot').classList.remove('dragging');
            }
        });

        // Setup drop events for drop zones
        document.addEventListener('dragover', (e) => {
            const dropZone = e.target.closest('.drop-zone');
            if (dropZone) {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            }
        });

        document.addEventListener('dragleave', (e) => {
            const dropZone = e.target.closest('.drop-zone');
            if (dropZone) {
                dropZone.classList.remove('drag-over');
            }
        });

        document.addEventListener('drop', (e) => {
            const dropZone = e.target.closest('.drop-zone');
            if (dropZone) {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                
                const draggedItem = e.dataTransfer.getData('text/plain');
                const requiredItem = dropZone.getAttribute('data-requires');
                
                if (draggedItem === requiredItem) {
                    this.useItem(draggedItem, dropZone);
                } else {
                    this.showMessage(`You need a ${requiredItem} for this!`, 'error');
                }
            }
        });
    }

    useItem(itemId, dropZone) {
        console.log('Using item:', itemId);
        
        // Find the marker and entity
        const marker = dropZone.closest('a-marker');
        const entity = marker.querySelector('.interactive-item');
        
        if (!entity) return;
        
        // Get animation and enable data
        const animationName = entity.getAttribute('data-animation');
        const enableRoom = entity.getAttribute('data-enable');
        
        // Remove item from inventory
        this.removeFromInventory(itemId);
        
        // Play animation if specified
        if (animationName) {
            this.playAnimation(entity, animationName);
        }
        
        // Enable new room if specified
        if (enableRoom) {
            this.enableRoom(enableRoom);
        }
        
        // Show success message
        this.showMessage(`Used ${itemId} successfully!`, 'success');
        
        // Remove the drop zone
        dropZone.remove();
    }

    removeFromInventory(itemId) {
        // Remove from inventory array
        const index = this.inventory.indexOf(itemId);
        if (index > -1) {
            this.inventory.splice(index, 1);
        }
        
        // Remove from inventory grid
        const slot = Array.from(this.inventorySlots).find(slot => 
            slot.getAttribute('data-item') === itemId
        );
        
        if (slot) {
            slot.innerHTML = '';
            slot.classList.remove('filled');
            slot.removeAttribute('data-item');
        }
    }

    playAnimation(entity, animationName) {
        console.log('Playing animation:', animationName);
        
        // Set animation mixer to play the specific animation
        entity.setAttribute('animation-mixer', `clip: ${animationName}; loop: once`);
        
        // You can add more sophisticated animation handling here
    }

    enableRoom(roomId) {
        console.log('Enabling room:', roomId);
        
        if (!this.enabledRooms.includes(roomId)) {
            this.enabledRooms.push(roomId);
            
            // Show markers for this room
            const roomMarkers = document.querySelectorAll(`[data-room="${roomId}"]`);
            roomMarkers.forEach(marker => {
                marker.style.display = 'block';
            });
            
            this.showMessage(`New area unlocked: ${roomId}!`, 'success');
        }
    }

    showMessage(text, type = 'success') {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.message');
        existingMessages.forEach(msg => msg.remove());
        
        // Create new message
        const message = document.createElement('div');
        message.className = `message ${type}`;
        message.textContent = text;
        
        document.body.appendChild(message);
        
        // Show message
        setTimeout(() => {
            message.classList.add('show');
        }, 100);
        
        // Hide message after 3 seconds
        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => {
                message.remove();
            }, 300);
        }, 3000);
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new SimpleARGame();
});
