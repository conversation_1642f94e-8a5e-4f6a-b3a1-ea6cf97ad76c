// Primitives-only, no <a-assets>. Students touch HTML only.
(() => {
  console.log('Initializing AR Experience (primitives only)…');

  const btnCollect = document.getElementById('collectButton');
  const invSlots = [...document.querySelectorAll('.inventory-slot')];
  const doorDrop = document.getElementById('doorDrop');

  const keyMarker = document.getElementById('marker-key');
  const doorMarker = document.getElementById('marker-door');
  const treasureMarker = document.getElementById('marker-treasure');

  const keyEntity = document.getElementById('key-entity');
  const doorLeaf  = document.getElementById('door-leaf');
  const treasureEntity = document.getElementById('treasure-entity');

  let visibleCollectable = null; // 'key' | 'treasure'
  let collected = new Set();     // collected items
  let room2Enabled = false;
  let doorIsOpen = false;

  function emptySlot() { return invSlots.find(s => !s.classList.contains('filled')); }

  function addChip(label) {
    const slot = emptySlot();
    if (!slot) return;
    slot.classList.add('filled');

    const chip = document.createElement('div');
    chip.className = 'item-chip';
    chip.draggable = true;
    chip.textContent = label.toUpperCase();
    chip.dataset.item = label.toLowerCase();

    chip.addEventListener('dragstart', e => {
      e.dataTransfer.setData('text/plain', chip.dataset.item);
      doorDrop.classList.add('active');
    });
    chip.addEventListener('dragend', () => {
      doorDrop.classList.remove('active');
    });

    slot.appendChild(chip);
  }

  btnCollect.addEventListener('click', () => {
    if (!visibleCollectable) return;

    if (visibleCollectable === 'key') {
      collected.add('key');
      addChip('key');
      keyEntity.setAttribute('visible', false);
    } else if (visibleCollectable === 'treasure' && room2Enabled) {
      collected.add('treasure');
      addChip('treasure');
      treasureEntity.setAttribute('visible', false);
    }
    visibleCollectable = null;
    btnCollect.style.display = 'none';
  });

  doorDrop.addEventListener('dragover', e => e.preventDefault());
  doorDrop.addEventListener('drop', e => {
    e.preventDefault();
    const item = e.dataTransfer.getData('text/plain');
    if (item === 'key' && !doorIsOpen) {
      doorLeaf.emit('open');        // triggers animation__open
      doorIsOpen = true;
      room2Enabled = true;
      doorDrop.style.display = 'none';
      console.log('Door opened → room2 enabled');
    }
  });

  function wireCollectable(markerEl, entityEl, name) {
    markerEl.addEventListener('markerFound', () => {
      if (name === 'treasure' && !room2Enabled) return;
      if (!collected.has(name)) {
        visibleCollectable = name;
        entityEl.setAttribute('visible', true);
        btnCollect.style.display = 'block';
      }
    });
    markerEl.addEventListener('markerLost', () => {
      if (visibleCollectable === name) {
        visibleCollectable = null;
        btnCollect.style.display = 'none';
      }
    });
  }

  wireCollectable(keyMarker, keyEntity, 'key');
  wireCollectable(treasureMarker, treasureEntity, 'treasure');

  doorMarker.addEventListener('markerFound', () => {
    if (collected.has('key') && !doorIsOpen) {
      doorDrop.style.display = 'grid';
    }
  });
  doorMarker.addEventListener('markerLost', () => {
    doorDrop.style.display = 'none';
  });

  // default visibility
  keyEntity.setAttribute('visible', true);
  treasureEntity.setAttribute('visible', true);
})();
