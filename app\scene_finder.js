function updateSceneInfoDisplay() {
  document.getElementById("currentScene").textContent = currentScene;
  document.getElementById("currentSubscene").textContent = subSceneIndex;
}

// Call this function whenever you change the scene or subscene, for example:
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
  updateSceneInfoDisplay(); // Update the display
}

function resetCurrentSceneToInitialSubscene() {
  // Reset logic...
  subSceneIndex = 0; // Reset to the first subscene
  updateSceneInfoDisplay(); // Update the display
}

function completeScene(nextScene, ...actions) {
  currentScene = nextScene;
  subSceneIndex = 0; // Reset subSceneIndex for the new scene
  actions.forEach((action) => action());
  updateSceneInfoDisplay(); // Update the display
}

// Make sure to call updateSceneInfoDisplay() after initializing the game to display the initial scene and subscene:
document.addEventListener("DOMContentLoaded", (event) => {
  console.log("DOM fully loaded and parsed");
  resetGame();
});

