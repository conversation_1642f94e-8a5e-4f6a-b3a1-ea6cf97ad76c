* { box-sizing: border-box; margin: 0; padding: 0; }
html, body { width: 100%; height: 100%; background: #000; font-family: system-ui, Arial, sans-serif; }
a-scene { width: 100vw; height: 100vh; }

/* Inventory */
#inventory {
  position: fixed;
  top: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  padding: 8px;
  border: 2px solid #fff;
  border-radius: 10px;
  background: rgba(0,0,0,0.6);
  z-index: 10;
}
.inventory-slot {
  width: 56px;
  height: 56px;
  border: 2px solid #ccc;
  border-radius: 8px;
  display: grid;
  place-items: center;
  color: #fff;
  font-weight: 700;
  font-size: 12px;
  user-select: none;
}
.inventory-slot.filled { border-color: #4CAF50; background: rgba(76,175,80,0.25); }

/* Draggable “chips” */
.item-chip {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 999px;
  border: 1px solid #fff;
  font-size: 11px;
  font-weight: 700;
  cursor: grab;
  background: rgba(255,255,255,0.12);
}

/* Collect button */
#collectButton {
  position: fixed;
  bottom: 90px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  background: #4CAF50;
  color: #fff;
  border: none;
  padding: 12px 22px;
  border-radius: 999px;
  font-size: 16px;
  font-weight: 800;
  cursor: pointer;
  box-shadow: 0 6px 16px rgba(76,175,80,0.45);
}
#collectButton:active { transform: translateX(-50%) scale(0.98); }

/* Door drop zone overlay (visible when door marker is seen) */
.drop-zone {
  position: fixed;
  inset: 0;
  display: grid;
  place-items: center;
  background: rgba(255, 215, 0, 0.08);
  border: 3px dashed rgba(255, 215, 0, 0.7);
  z-index: 9;
  pointer-events: none;
}
.drop-zone span {
  color: #FFD700;
  background: rgba(0,0,0,0.6);
  border: 1px solid #FFD700;
  padding: 6px 10px;
  border-radius: 8px;
  font-weight: 800;
}
.drop-zone.active { pointer-events: all; background: rgba(255, 215, 0, 0.14); }

/* Small screens */
@media (max-width: 768px) {
  #inventory { gap: 8px; padding: 6px; }
  .inventory-slot { width: 50px; height: 50px; }
  #collectButton { bottom: 70px; font-size: 15px; padding: 10px 18px; }
}
