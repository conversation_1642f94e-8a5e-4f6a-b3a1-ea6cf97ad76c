/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    overflow: hidden;
    background: #000;
}

/* Inventory Grid */
#inventory {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 10px;
    border: 2px solid #fff;
}

.inventory-slot {
    width: 60px;
    height: 60px;
    border: 2px solid #ccc;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
}

.inventory-slot:hover {
    border-color: #fff;
    background: rgba(255, 255, 255, 0.2);
}

.inventory-slot.filled {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.3);
}

.inventory-slot img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 4px;
}

.inventory-slot.dragging {
    opacity: 0.5;
    transform: scale(1.1);
}

/* Collect Button */
#collectButton {
    position: fixed;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    background: #4CAF50;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 18px;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transition: all 0.3s ease;
}

#collectButton:hover {
    background: #45a049;
    transform: translateX(-50%) scale(1.05);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

#collectButton:active {
    transform: translateX(-50%) scale(0.95);
}

/* Drop zones for interaction */
.drop-zone {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 3px dashed transparent;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.drop-zone.drag-over {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.2);
}

/* Success/Error messages */
.message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2000;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px 40px;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.message.show {
    opacity: 1;
}

.message.success {
    border: 2px solid #4CAF50;
    color: #4CAF50;
}

.message.error {
    border: 2px solid #f44336;
    color: #f44336;
}

/* AR Scene styling */
a-scene {
    width: 100vw;
    height: 100vh;
}

/* Loading indicator */
.loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1500;
    color: white;
    font-size: 18px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    #inventory {
        top: 10px;
        gap: 5px;
        padding: 8px;
    }
    
    .inventory-slot {
        width: 50px;
        height: 50px;
    }
    
    #collectButton {
        bottom: 80px;
        padding: 12px 24px;
        font-size: 16px;
    }
}
