document.addEventListener("DOMContentLoaded", () => {
  setupEventListeners();
  resetGame(); // Initialize the game state
});

// Global variable to control active markers based on the scene
let activeMarkers = {
  vendingMachine: true, // Initially, only vendingMachine is active
};

// Marker Definitions with more descriptive object keys
const actionMarkers = {
  vendingMachine: {
    element: document.querySelector("#vending-machine"),
    visible: false,
  },
  // Initially, other markers are defined but not active
  boat: { element: document.querySelector("#boat"), visible: false, active: false },
  bush: { element: document.querySelector("#bush"), visible: false, active: false },
  shop: { element: document.querySelector("#shop"), visible: false, active: false },
  pocket: { element: document.querySelector("#pocket"), visible: false, active: false },
  coin: { element: document.querySelector("#coin"), visible: false, active: false },
  ball: { element: document.querySelector("#ball"), visible: false, active: false },
  ballOne: { element: document.querySelector("#ball-one"), visible: false, active: false },
  ticket: { element: document.querySelector("#ticket"), visible: false, active: false },
};

let currentScene = "vendingMachine"; // Initial scene set correctly
let subSceneIndex = 0; // Start at the first subscene

// Scene definitions with corrected action references
const scenes = {
  vendingMachine: {
    actions: [
      {
        pocket: () => moveToSubScene(1, insertCoin),
        coin: () => {}, // No action
        ball: () => {}, // No action
      },
      {
        // Subscene 2 (skrituļdēlis)
        pocket: () => {}, // No action
        coin: () => completeScene("ballOne", showBallOne, hideVendingMachine),
        ball: () => resetCurrentSceneToInitialSubscene(),
      },
    ],
  },
  ballOne: {
    actions: [
      {
        marker1: () => {},
        "kubs-marker": () => completeScene("cube", hideBallOne, showCube),
        marker3: () => {},
      },
    ],
  },

  cube: {
    actions: [
      {
        // Subscene 1
        marker2: () => moveToSubScene(1, moveCubeX),
        marker1: () => {}, // No action
        marker3: () => {}, // No action
      },
      {
        // Subscene 2
        marker2: () => {}, // No action
        marker1: () => moveToSubScene(2, moveCubeZ),
        marker3: () => resetCurrentSceneToInitialSubscene(),
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => {}, // No action
        marker3: () => completeScene("cone", hideCube, showCone),
      },
    ],
  },
  cone: {
    actions: [
      {
        // Subscene 1
        marker2: () => {}, // No action,
        marker1: () => {}, // No action
        marker3: () => moveToSubScene(1, moveConeX),
      },
      {
        // Subscene 2
        marker2: () => moveToSubScene(2, moveConeZ),
        marker1: () => resetCurrentSceneToInitialSubscene(),
        marker3: () => {}, // No action
      },
      {
        // Subscene 3
        marker2: () => {}, // No action
        marker1: () => completeScene("cone", hideCone),
        marker3: () => {}, // No action
      },
    ],
  },
};

// Setup event listeners for each marker
function setupEventListeners() {
  Object.entries(actionMarkers).forEach(([markerName, markerInfo]) => {
    if (!markerInfo.element) {
      console.warn(`Element for marker '${markerName}' not found.`);
      return;
    }

    markerInfo.element.addEventListener("markerFound", () => {
      if (activeMarkers[markerName]) { // Check if marker is active
        console.log(`${markerName} found`);
        markerInfo.visible = true;
        handleMarkerInteraction(markerName);
      }
    });

    markerInfo.element.addEventListener("markerLost", () => {
      if (activeMarkers[markerName]) { // Check if marker is active
        markerInfo.visible = false;
      }
    });
  });
}

// Function to handle scene transitions based on marker interaction
function handleMarkerInteraction(markerId) {
  const currentActions = scenes[currentScene]?.actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    console.log(`Executing action for: ${markerId}`);
    currentActions[markerId]();
  } else {
    console.log(
      `No action defined for ${markerId} in scene: ${currentScene}, Subscene: ${subSceneIndex}`
    );
  }
}

// Utility functions to handle scene logic
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
  console.log(`Moved to subscene ${newIndex} in scene ${currentScene}`);
}

function completeScene(nextScene) {
  currentScene = nextScene;
  subSceneIndex = 0; // Start at the first subscene of the new scene
  console.log(`Scene changed to ${nextScene}`);
}

function resetCurrentSceneToInitialSubscene() {
  // Reset all relevant objects and state for the current scene
  resetCylinderPosition(); // Reset the cylinder to its initial position
  resetCubePosition();
  resetConePosition(); // Add any additional reset logic here for other objects or state variables
  resetBallPosition();

  subSceneIndex = 0; // Reset to the first subscene

  // If you have any visible/invisible toggles based on subscenes, reset those here
  // For example:
  // document.querySelector("#cilindrs").setAttribute("visible", true);
  // Adjust visibility as needed for your scene setup

  console.log(`Scene updated to: ${currentScene}, Subscene: ${subSceneIndex}`);

  // Reset and initialize the game

  function animateMove(object, targetPosition, duration) {
    let startTime = null;
    const startPosition = object.getAttribute("position");

    function animate(time) {
      if (!startTime) startTime = time;
      const elapsedTime = time - startTime;
      const fraction = elapsedTime / duration;

      if (fraction < 1) {
        let newX =
          startPosition.x + (targetPosition.x - startPosition.x) * fraction;
        object.setAttribute("position", {
          x: newX,
          y: startPosition.y,
          z: startPosition.z,
        });
        requestAnimationFrame(animate);
      } else {
        // Ensure the final position is set
        object.setAttribute("position", targetPosition);
      }
    }

    requestAnimationFrame(animate);
  }

  function insertCoin() {
    console.log(
      "Coin inserted - Transition to next part of the scene or subscene"
    );
    let cylinder = document.querySelector("#vending-machine");
    let currentPosition = cylinder.getAttribute("position");
    let targetPosition = {
      x: currentPosition.x + 1,
      y: currentPosition.y,
      z: currentPosition.z,
    };
    animateMove(cylinder, targetPosition, 1000); // 1000 ms for the animation
  }

  function showBallOne() {
    let ballOne = document.querySelector("#ball-one");
    if (ballOne) {
      ballOne.setAttribute("visible", true);
    } else {
      console.error("Element #ball-one not found");
    }
  }

  function moveCylinderZ() {
    let cylinder = document.querySelector("#vending-machine");
    let currentPosition = cylinder.getAttribute("position");
    cylinder.setAttribute("position", {
      x: currentPosition.x,
      y: currentPosition.y,
      z: currentPosition.z - 1,
    });
  }

  function moveCubeX() {
    let cube = document.querySelector("#kubs");
    let currentPosition = cube.getAttribute("position");
    let targetPosition = {
      x: currentPosition.x + 1,
      y: currentPosition.y,
      z: currentPosition.z,
    };
    animateMove(cube, targetPosition, 1000); // 1000 ms for the animation
  }

  function moveCubeZ() {
    let cube = document.querySelector("#kubs");
    let currentPosition = cube.getAttribute("position");
    cube.setAttribute("position", {
      x: currentPosition.x,
      y: currentPosition.y,
      z: currentPosition.z - 1,
    });
  }
  function moveConeX() {
    let cone = document.querySelector("#konuss");
    let currentPosition = cone.getAttribute("position");
    let targetPosition = {
      x: currentPosition.x + 1,
      y: currentPosition.y,
      z: currentPosition.z,
    };
    animateMove(cone, targetPosition, 1000); // 1000 ms for the animation
  }

  function moveConeZ() {
    let cone = document.querySelector("#konuss");
    let currentPosition = cone.getAttribute("position");
    cone.setAttribute("position", {
      x: currentPosition.x,
      y: currentPosition.y,
      z: currentPosition.z - 1,
    });
  }

  function resetToSubScene(newIndex) {
    subSceneIndex = newIndex;
    resetCylinderPosition(); // This will reset the position
    resetCubePosition();
    resetConePosition();
    resetBallPosition();
  }

  function resetCylinderPosition() {
    // Reset cylinder position to the start position
    document
      .querySelector("#vending-machine")
      .setAttribute("position", { x: 0, y: 0, z: 0 });
  }

  function resetCubePosition() {
    document
      .querySelector("#kubs")
      .setAttribute("position", { x: 0, y: 0, z: 0 });
  }

  function resetConePosition() {
    document
      .querySelector("#konuss")
      .setAttribute("position", { x: 0, y: 0, z: 0 });
  }
  function resetBallPosition() {
    document
      .querySelector("#ball")
      .setAttribute("position", { x: 0, y: 0, z: 0 });
  }

  function hideVendingMachine() {
    let cylinder = document.querySelector("#vending-machine");
    if (cylinder) {
      cylinder.setAttribute("visible", false);
    } else {
      console.error("Cylinder #vending-machine not found");
    }
  }

  function hideBallOne() {
    let ballOne = document.querySelector("#ball-one");
    if (ballOne) {
      ballOne.setAttribute("visible", false);
    } else {
      console.error("Element #ball-one not found");
    }
  }

  function hideBall() {
    let ball = document.querySelector("#ball");
    if (ball) {
      ball.setAttribute("visible", false);
    } else {
      console.error("Element #ball not found");
    }
  }

  function hideCube() {
    let kubs = document.querySelector("#kubs");
    if (kubs) {
      kubs.setAttribute("visible", false);
    } else {
      console.error("Element #kubs not found");
    }
  }

  function showCube() {
    document.querySelector("#kubs").setAttribute("visible", true);
  }
  function hideCone() {
    document.querySelector("#konuss").setAttribute("visible", false);
  }

  function showCone() {
    document.querySelector("#konuss").setAttribute("visible", true);
  }
  function showBall() {
    document.querySelector("#ball").setAttribute("visible", true);
  }
  function resetScene() {
    subSceneIndex = 0;
    resetCylinderPosition();
    resetCubePosition();
    resetConePosition();
    resetBallPosition();
    // Reset other objects if necessary
  }
  function setActiveMarkers(scene) {
  // Reset all markers to inactive
  Object.keys(actionMarkers).forEach(marker => {
    activeMarkers[marker] = false;
  });

  // Activate markers based on the current scene
  if (scene === "vendingMachine") {
    activeMarkers.vendingMachine = true;
  }
  // Add conditions for other scenes as necessary
  // e.g., activeMarkers.pocket = true; for a specific scene
}

  function resetGame() {
    // Reset game state to initial settings
    currentScene = "vendingMachine";
    subSceneIndex = 0;
    document.querySelector("#kubs").setAttribute("visible", false); // Explicitly hide the cube
    document.querySelector("#konuss").setAttribute("visible", false);
    document.querySelector("#vending-machine").setAttribute("visible", true); // Show cylinder initially
    document.querySelector("#ball-one").setAttribute("visible", false); // Hide ball one initially
    document.querySelector("#ball").setAttribute("visible", false); // Hide ball initially
    resetCylinderPosition();
    resetCubePosition();
    resetConePosition();
    resetBallPosition();
    // Reset visibility and positions of objects as necessary
  // Reset marker visibility and positions as necessary
  Object.values(actionMarkers).forEach(marker => {
    if (marker.element) {
      marker.element.setAttribute("visible", false); // Hide all markers initially
      marker.visible = false;
    }
  });

  // Ensure only the vending machine marker is active initially
  setActiveMarkers(currentScene);

  console.log("Game reset to initial state");
}


  // A-Frame Component for Continuous Tracking
  AFRAME.registerComponent("continuous-tracking", {
    tick: function () {
      // Optional: Continuous tracking logic
    },
  });

  document.querySelector("a-scene").setAttribute("continuous-tracking", "");
}
