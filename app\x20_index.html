<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>AR Game</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="styles.css">
    <script src="https://aframe.io/releases/1.2.0/aframe.min.js"></script>
    <script src="https://jeromeetienne.github.io/AR.js/aframe/build/aframe-ar.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v6.1.1/dist/aframe-extras.min.js"></script>
  </head>
  <body style="margin: 0; overflow: hidden">
    <p>
        <div id="sceneInfo" style="position: absolute; top: 0; left: 0; color: white; background: rgba(0, 0, 0, 0.5);padding: 10px; z-index: 100;">
          Scene Info: <span id="currentScene">None</span> | Subscene:
          <span id="currentSubscene">None</span>
        </div>
        <div id="markersInfo" style=" position: absolute; top: 0; right: 0; color: white; background: rgba(0, 0, 0, 0.5); padding: 10px; z-index: 100;">
          Markers Info: <span id="currentMarker">None</span>
        </div>
        <div id="log" style="position: absolute; bottom: 0; right: 0; color: white; background: rgba(0, 0, 0, 0.5);padding: 10px; z-index: 100;">
        </div>

        <div id="imageContainer">
          <img id="sceneImage" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079" alt="Scene Image">
        </div>

        <b>
          <div
            id="message"
            style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
              color: red; font-family: Arial; padding: 20px; z-index: 100; font-family: pixel; font-size: 2em;">
            Message goes here
          </div>
        </b>
    </p>

      <a-scene
        embedded
        arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 3x3; debugUIEnabled: false;"
      >
      
      <!-- Nosaukti un ieladeti visi aseti -->
      <a-assets>
<!--         <a-asset-item id="vending-machine-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/VM-animation.glb?v=1713215286413"></a-asset-item> -->
        <a-asset-item id="vm-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/VM%20bumbu%20animacija(1).glb?v=1716028398861"></a-asset-item>
        <a-asset-item id="wallet-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Wallet-animation.glb?v=1713977053842"></a-asset-item>
        <a-asset-item id="coin-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Coin-animation.glb?v=1713976934952"></a-asset-item>
        <a-asset-item id="ball-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Ball-animation.glb?v=1715853249228"></a-asset-item>
        <a-asset-item id="boat-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Boat-animation.glb?v=1716022948603"></a-asset-item>
        <a-asset-item id="bush-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Bush-animations(1).glb?v=1715853252032"></a-asset-item>
        <a-asset-item id="shop-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Shop-animation.glb?v=1713215285172"></a-asset-item>
        <a-asset-item id="hg1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-1-animation.glb?v=1713215282063"></a-asset-item>
        <a-asset-item id="hg2-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-5-animation.glb?v=1713215283064"></a-asset-item>
        <a-asset-item id="hg3-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-6-animation.glb?v=1713215284083"></a-asset-item>
        <a-asset-item id="hg4-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Hourglass-7-animation.glb?v=1713215284622"></a-asset-item>
        <a-asset-item id="clock-handle1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_1handle.glb?v=1716025321442"></a-asset-item>
        <a-asset-item id="clock-handle2-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_2handles.glb?v=1716028755041"></a-asset-item>
        <a-asset-item id="clock-ticking-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_ticking.glb?v=1716031157465"></a-asset-item>
        <a-asset-item id="clock-brocken-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_brocken.glb?v=1716025325591"></a-asset-item>
        <a-asset-item id="clock-brockenA-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/clock_brockenA.glb?v=1716031753191"></a-asset-item>
        <a-asset-item id="chest-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Chest-animation.glb?v=1713216521118"></a-asset-item>
        <a-asset-item id="ticket-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Ticket-animation.glb?v=1713215285722"></a-asset-item>
        <a-asset-item id="key1-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF01-animation.glb?v=1713716051759"></a-asset-item>
        <a-asset-item id="key2-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF02-animation.glb?v=1713716050624"></a-asset-item>
        <a-asset-item id="key3-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KeyF03-animation.glb?v=1713716051011"></a-asset-item>
        <a-asset-item id="bus-model" src="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/Bus-animation.glb?v=1713215289721"></a-asset-item>
      </a-assets>

      <!-- Items -->
      <a-marker
        id="wallet"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Pocket1.patt?v=1715426546828"
        preset="custom"
      >
        <a-entity
          id="wallet-entity"
          gltf-model="#wallet-model"
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="bush1"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Bush01_1.patt?v=1715426537652"
        preset="custom"
      >
      </a-marker>
        
              <a-marker
        id="bush2"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Bush02_2.patt?v=1715426538480"
        preset="custom"
      >
      </a-marker>
        
              <a-marker
        id="bush3"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Bush03_3.patt?v=1715426538920"
        preset="custom"
      >
      </a-marker>
        
      <a-marker
        id="hg1"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-HG01_1.patt?v=1715426540909"
        preset="custom"
      >
        <a-entity
          id="hg1-entity" scale=".5 .5 .5" gltf-model="#hg1-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
              <a-marker
        id="hg2"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-HG02_2.patt?v=1715426544305"
        preset="custom"
      >
        <a-entity
          id="hg2-entity" scale=".5 .5 .5" gltf-model="#hg2-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
              <a-marker
        id="hg3"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-HG03_3.patt?v=1715426544743"
        preset="custom"
      >
        <a-entity
          id="hg3-entity" scale=".5 .5 .5" gltf-model="#hg3-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
              <a-marker
        id="hg4"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-HG04_4.patt?v=1715426545135"
        preset="custom"
      >
        <a-entity
          id="hg4-entity" scale=".5 .5 .5" gltf-model="#hg4-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
           <a-marker
        id="key1"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Key01_1.patt?v=1715426545570"
        preset="custom"
      >
        <a-entity
          id="key1-entity" gltf-model="#key1-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
              <a-marker
        id="key2"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Key02_2.patt?v=1715426545992"
        preset="custom"
      >
        <a-entity
          id="key2-entity" gltf-model="#key2-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
              <a-marker
        id="key3"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Key03_3.patt?v=1715426546413"
        preset="custom"
      >
        <a-entity
          id="key3-entity" gltf-model="#key3-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
        
      <a-marker
        id="coin"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Coin1.patt?v=1715426540211"
        preset="custom"
      >
       <a-entity
          id="coin-entity" scale= ".5 .5 .5" gltf-model="#coin-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="ball"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Ball1.patt?v=1715426548592"
        preset="custom"
      >
        <a-entity
          id="ball-entity" rotation="0 -90 0" scale=".75 .75 .75" gltf-model="#ball-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="ticket"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Ticket1.patt?v=1715426547706"
      >
        <a-entity
          id="ticket-entity" gltf-model="#ticket-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>

      
            <a-marker
        id="vm"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-VM1.patt?v=1715426548149"
        preset="custom"
      >
        <a-entity
          id="vm-entity"
          gltf-model="#vm-model"
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>

      
      <a-marker
        id="boat"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Boat1.patt?v=1715426549025"
        preset="custom"
      > 
        <a-entity
          id="boat-entity"
          gltf-model="#boat-model"
          scale= "0.4 0.4 0.4"
          rotation="0 0 0"
           
          animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
     <a-marker
        id="bush"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Bush1.patt?v=1715426538088"
        preset="custom"
      >
        <a-entity
          id="bush-entity" scale=".3 .3 .3" rotation="0 180 0" gltf-model="#bush-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
<!--         position="1 0 0" -->
        
      <a-marker
        id="shop"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Shop1.patt?v=1715426547250"
        preset="custom"
      >
        <a-entity
          id="shop-entity" gltf-model="#shop-model" scale="0.15 .15 0.15" rotation="0 180 0" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
            <a-marker
        id="clock"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Clock1.patt?v=1715426539777"
        preset="custom"
      >
        <a-entity
          id="clock-brocken-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-brocken-model" animation-mixer="loop: repeat"
        ></a-entity>
              
         <a-entity
          id="clock-brockenA-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-brockenA-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-handle1-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-handle1-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-handle2-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-handle2-model" animation-mixer="loop: repeat"
        ></a-entity>
              
        <a-entity
          id="clock-ticking-entity" scale=".5 .5 .5" rotation="0 -90 0" gltf-model="#clock-ticking-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="chest"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Chest1.patt?v=1715426539343"
        preset="custom"
      >
        <a-entity
          id="chest-entity" scale=".5 .5 .5" rotation="0 180 0" gltf-model="#chest-model" animation-mixer="loop: repeat"
        ></a-entity>
      </a-marker>
      
      <a-marker
        id="bus"
        type="pattern"
        url="https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/pattern-Bus1.patt?v=1715426537193"
        preset="custom"
                >
        <a-entity
          id="bus-entity"  scale=".25 .25 .25" gltf-model="#bus-model" animation-mixer="loop: repeat"
        ></a-entity>     
      </a-marker>


      <a-entity camera></a-entity>
    </a-scene>
  

    <div id="storyContainer">
      <div id="storyText"></div>
      <div id="buttonsContainer">
        <button id="backButton">Atpakaļ</button>
        <button id="nextButton">Tālāk</button>
      </div>
    </div>
  
    <div id="scanButtonContainer">
    <button id="scanButton">Skenēt</button>
  </div>
  
    <div id="timerContainer">
    <span id="timerText"></span>
  </div>
    
    <script src="markers.js"></script>
    <script src="scene_finder.js"></script>
    <script src="marker-finder.js"></script>

  </body>
</html>
