<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>AR.js barcode markers + A-Frame</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- A-Frame -->
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <!-- AR.js for A-Frame -->
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js/aframe/build/aframe-ar.min.js"></script>
  </head>
  <body style="margin:0; overflow:hidden">
    <!-- Choose the matrixCodeType to match the folder in the repo -->
    <!-- Options include: 3x3, 3x3_HAMMING63, 3x3_PARITY65, 4x4_BCH_13_9_3, 4x4_BCH_13_5_5 -->
    <a-scene
      embedded
      vr-mode-ui="enabled: false"
      renderer="precision: medium; antialias: true"
      arjs="sourceType: webcam; detectionMode: mono_and_matrix; matrixCodeType: 4x4_BCH_13_9_3;"
    >
      <!-- Use a barcode marker from the repo; value=N is the filename number -->
      <!-- e.g., if you print 4x4_bch_13_9_3/barcode_25.png then value="25" -->
      <a-marker type="barcode" value="25">
        <a-box position="0 0.5 0" depth="1" height="1" width="1" color="#4CC3D9"></a-box>
      </a-marker>

      <!-- Always include a camera -->
      <a-entity camera></a-entity>
    </a-scene>
  </body>
</html>