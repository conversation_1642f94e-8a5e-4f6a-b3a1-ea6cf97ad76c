# Simple AR Game for Students

This is a simplified AR game system designed for educational purposes. Students can create AR experiences by modifying only the HTML file, while all the complex JavaScript logic is handled automatically.

## How It Works

### For Students (HTML Only)

Students only need to modify the `index.html` file to create their AR game. The system uses simple, descriptive attributes:

#### Basic Asset Structure:
```html
<a-asset-item
    id="unique-id"
    png="png/item-icon.png"
    glb="glb/3d-model.glb"
    collectable
    connect="other-item-id"
    marker="type='barcode' value='25'"
></a-asset-item>
```

#### Interactive Object Structure:
```html
<a-asset-item
    id="door"
    src="assets/door.glb"
    marker="type='barcode' value='24'"
    animation="open"
    enable="room2"
></a-asset-item>
```

### Attributes Explained:

- **`id`**: Unique identifier for the item
- **`png`**: Path to the 2D icon shown in inventory
- **`glb`**: Path to the 3D model shown in AR
- **`collectable`**: Makes the item collectable (shows collect button)
- **`connect`**: Which item this connects to (for drag & drop)
- **`marker`**: Which barcode/marker triggers this item
- **`animation`**: Animation name to play when activated
- **`enable`**: Which room/area to unlock when used
- **`room`**: Which room this item belongs to
- **`disabled`**: Item starts disabled

### Marker Types:

1. **Hiro Marker**: `preset="hiro"` (default AR.js marker)
2. **Barcode Markers**: `type="barcode" value="25"` (numbers 0-63)

### Game Flow:

1. **Scan Marker** → 3D model appears
2. **Collect Item** → Item goes to inventory grid
3. **Drag & Drop** → Use items on other objects
4. **Trigger Actions** → Play animations, unlock areas

## File Structure:

```
simple_version_AR/
├── index.html          # Main file (students edit this)
├── styles.css          # Styling (pre-made)
├── game-logic.js       # Game logic (students don't touch)
├── png/               # 2D inventory icons
│   ├── key.png
│   └── treasure.png
├── glb/               # 3D models
│   ├── key.glb
│   └── treasure.glb
└── assets/            # Additional assets
    └── door.glb
```

## Example Game:

The included example demonstrates:

1. **Hiro Marker**: Shows animated cube (demo)
2. **Barcode 25**: Key that can be collected
3. **Barcode 24**: Door that requires the key
4. **Barcode 26**: Treasure in room2 (unlocked after using key)

## For Teachers:

### Setting Up:

1. Students get the complete folder
2. They only modify `index.html`
3. They add their own PNG and GLB files
4. Test using a local web server

### Assignment Ideas:

- **Treasure Hunt**: Collect items to unlock areas
- **Puzzle Game**: Use items in correct sequence
- **Story Adventure**: Progress through rooms
- **Educational Content**: Learn by collecting and using items

### Barcode Markers:

Students can print barcode markers from:
- AR.js barcode generator
- Use values 0-63
- Each student can use different numbers

## Technical Notes:

- Uses A-Frame + AR.js
- Works on mobile devices with camera
- Requires HTTPS for camera access
- All game logic is pre-written
- Students focus on content, not code

## Getting Started:

1. Open `create_sample_assets.html` to generate sample PNG files
2. Place PNG files in `png/` folder
3. Place GLB files in `glb/` folder
4. Modify `index.html` to add your items
5. Test with barcode markers

## Troubleshooting:

- **Camera not working**: Ensure HTTPS and camera permissions
- **Models not loading**: Check file paths and formats
- **Markers not detected**: Ensure good lighting and steady camera
- **Items not collecting**: Check that `collectable` attribute is present
