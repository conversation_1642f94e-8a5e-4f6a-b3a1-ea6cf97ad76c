# Student Guide: Simple AR Experience Creation

## Quick Start

You only need to edit the `index.html` file to create your AR experience. All the programming is already done!

## Step 1: Understanding the HTML Structure

Look for the `<a-assets>` section in `index.html`. This is where you define your game items:

```html
<a-asset-item
    id="key"
    png="png/key.svg"
    collectable
    connect="door"
    marker="type='barcode' value='25'"
></a-asset-item>
```

## Step 2: Item Attributes

### Required Attributes:
- **`id`**: Give each item a unique name (no spaces!)
- **`marker`**: Which barcode shows this item

### Optional Attributes:
- **`png`**: Icon shown in inventory (put in `png/` folder)
- **`collectable`**: Makes item collectable
- **`connect`**: Which item this works with
- **`animation`**: Animation name to play
- **`enable`**: Unlock new area
- **`room`**: Which room this belongs to
- **`disabled`**: Starts hidden

## Step 3: Marker Types

### Hiro Marker (Default):
```html
<a-marker preset="hiro">
    <!-- Your 3D object here -->
</a-marker>
```

### Barcode Markers:
```html
<a-marker type="barcode" value="25">
    <!-- Your 3D object here -->
</a-marker>
```
Use numbers 0-63 for different barcodes.

## Step 4: Creating 3D Objects

Instead of complex 3D models, use simple shapes:

### Basic Shapes:
```html
<!-- Box -->
<a-box width="1" height="1" depth="1" color="red"></a-box>

<!-- Sphere -->
<a-sphere radius="0.5" color="blue"></a-sphere>

<!-- Cylinder -->
<a-cylinder radius="0.3" height="1" color="green"></a-cylinder>

<!-- Cone -->
<a-cone radius-bottom="0.5" height="1" color="yellow"></a-cone>
```

### Combining Shapes:
```html
<a-entity>
    <a-box position="0 0 0" color="brown"></a-box>
    <a-sphere position="0 1 0" color="gold"></a-sphere>
</a-entity>
```

## Step 5: Complete Example

Here's a simple treasure hunt game:

```html
<!-- Collectable Key -->
<a-marker type="barcode" value="25" id="marker-key">
    <a-entity class="collectable-item" data-item="key">
        <a-cylinder radius="0.1" height="1" color="#FFD700"></a-cylinder>
        <a-box position="0 0.6 0" width="0.5" height="0.3" depth="0.1" color="#FFD700"></a-box>
    </a-entity>
</a-marker>

<!-- Door that needs key -->
<a-marker type="barcode" value="24" id="marker-door">
    <a-entity class="interactive-item" data-item="door" data-requires="key" data-enable="room2">
        <a-box width="1.5" height="2" depth="0.1" color="#8B4513"></a-box>
        <a-sphere position="0.6 0 0.1" radius="0.05" color="#FFD700"></a-sphere>
    </a-entity>
</a-marker>

<!-- Treasure in room 2 -->
<a-marker type="barcode" value="26" id="marker-treasure" data-room="room2" style="display: none;">
    <a-entity class="collectable-item" data-item="treasure">
        <a-box width="1" height="0.6" depth="0.7" color="#8B4513"></a-box>
        <a-sphere position="0 0.5 0" radius="0.1" color="#FFD700"></a-sphere>
    </a-entity>
</a-marker>
```

## Step 6: Game Flow

1. **Scan barcode 25** → See key → Click "Collect" → Key goes to inventory
2. **Scan barcode 24** → See door → Drag key from inventory to door → Door opens, room2 unlocked
3. **Scan barcode 26** → See treasure (now available) → Collect treasure

## Step 7: Adding Your Own Items

1. **Choose a barcode number** (0-63, don't repeat!)
2. **Create your 3D object** using basic shapes
3. **Add the marker** to your HTML
4. **Test with printed barcode**

### Example - Adding a Sword:
```html
<a-marker type="barcode" value="30" id="marker-sword">
    <a-entity class="collectable-item" data-item="sword">
        <a-cylinder position="0 0 0" radius="0.05" height="1.5" color="brown"></a-cylinder>
        <a-box position="0 0.8 0" width="0.8" height="0.1" depth="0.05" color="silver"></a-box>
        <a-box position="0 0.9 0" width="0.2" height="0.1" depth="0.1" color="gold"></a-box>
    </a-entity>
</a-marker>
```

## Step 8: Testing Your Game

1. **Save your HTML file**
2. **Print barcode markers** from AR.js barcode generator
3. **Open in web browser** (needs camera permission)
4. **Point camera at markers**

## Tips:

- **Keep it simple**: Start with basic shapes
- **Use different colors**: Makes items easy to identify
- **Test frequently**: Check each marker works
- **Plan your story**: What happens in what order?
- **Use unique IDs**: No spaces, no special characters

## Common Problems:

- **Marker not detected**: Check lighting, hold steady
- **Item not collecting**: Add `class="collectable-item" data-item="itemname"`
- **Drag & drop not working**: Add `class="interactive-item" data-requires="itemname"`
- **Room not unlocking**: Add `data-enable="roomname"` and `data-room="roomname"`

## Assignment Ideas:

- **Museum Tour**: Collect artifacts, unlock exhibits
- **Science Lab**: Mix chemicals, unlock experiments  
- **History Adventure**: Collect historical items, progress through time periods
- **Math Puzzle**: Solve equations to unlock next level
- **Language Learning**: Collect words, build sentences

Have fun creating your AR adventure!
