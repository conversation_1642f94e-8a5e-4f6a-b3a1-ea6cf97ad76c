const markersInfo = {
  vm: { displayName: "Vending Machine", additionalInfo: "Info about Vending Machine" },
  boat: { displayName: "Boat", additionalInfo: "Info about Boat" },
  bush: { displayName: "Bush", additionalInfo: "Info about Bush" },
  shop: { displayName: "Shop", additionalInfo: "Info about Shop" },
  clock: { displayName: "clock", additionalInfo: "Info about Clock" },
  key1: { displayName: "#key1"},
  key2: { displayName: "#key2"  },
  key3: { displayName:"#key3" },
  hg1: { displayName: "#hg1"},
  hg2: { displayName: "#hg2"  },
  hg3: { displayName:"#hg3" },
  hg4: {displayName: "#hg4" },
  bush1: { displayName: "#bush1"},
  bush2: { displayName: "#bush2"  },
  bush3: { displayName:"#bush3" },
  boat: {  displayName: "#boat"  },
  bus: { displayName: "#bus" },
  wallet: {displayName: "#wallet", additionalInfo: "your pocket" },
  coin: {displayName: "#coin" },
  ball: {displayName: "#ball" },
  ball1: {displayName: "#ball-one" },
  ticket: {displayName: "#ticket"},

  // Add other markers as needed
};

function updateMarkersDisplay() {
  const displayArea = document.getElementById("markersInfo"); // Corrected ID to match HTML
  let content = "Markers Info: "; // Starting content

  Object.keys(markersInfo).forEach(markerId => {
    const marker = document.querySelector(`#${markerId}`);
    // Check if marker is visible within A-Frame/AR.js context
    if (marker && marker.getAttribute('visible')) { // Corrected visibility check
      const info = markersInfo[markerId];
      content += `<br>${info.displayName}: ${info.additionalInfo}`; // Appending info
    }
  });

  displayArea.innerHTML = content; // Updating HTML content
}

function setupEventListeners() {
  Object.keys(markersInfo).forEach(markerId => {
    const marker = document.querySelector(`#${markerId}`);
    if (!marker) {
      console.warn(`Marker with ID ${markerId} not found.`);
      return;
    }

    marker.addEventListener("markerFound", () => {
      console.log(`Marker found: ${markerId}`);
      marker.setAttribute('visible', true); // Correct way to set visibility
      updateMarkersDisplay();
    });

    marker.addEventListener("markerLost", () => {
      console.log(`Marker lost: ${markerId}`);
      marker.setAttribute('visible', false); // Correct way to set visibility
      updateMarkersDisplay();
    });
  });
}

document.addEventListener("DOMContentLoaded", () => {
  setupEventListeners();
});
