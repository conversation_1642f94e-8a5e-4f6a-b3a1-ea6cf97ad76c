// Marker Definitions
var actionMarkers = {
  pocket: { element: document.querySelector("#pocket"), visible: false },
  coin: { element: document.querySelector("#coin"), visible: false },
  ball: { element: document.querySelector("#ball"), visible: false },
  boat: { element: document.querySelector("#boat"), visible: false },
  bush: { element: document.querySelector("#bush"), visible: false },
  one: { element: document.querySelector("#one"), visible: false },
  two: { element: document.querySelector("#two"), visible: false },
  three: { element: document.querySelector("#three"), visible: false },
  four: { element: document.querySelector("#four"), visible: false },
  shop: { element: document.querySelector("#shop"), visible: false },
  clock: { element: document.querySelector("#clock"), visible: false },
  chest: { element: document.querySelector("#chest"), visible: false },
  ticket: { element: document.querySelector("#ticket"), visible: false },
  bus: { element: document.querySelector("#bus"), visible: false },
vendingMachine: { element: document.querySelector("#vending-machine"), visible: false },

};


var currentScene = "scene0";
var subSceneIndex = 0;

// Initialize the game
resetGame();

// Event Listeners for Markers
Object.values(actionMarkers).forEach((marker) => {
  marker.element.addEventListener("markerFound", () => {
    marker.visible = true;
    handleMarkerInteraction(marker.element.id);
  });
  marker.element.addEventListener("markerLost", () => {
    marker.visible = false;
  });
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
  console.log(
    `Handling interaction for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
  );
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    console.log(`Executing action for marker: ${markerId}`);
    currentActions[markerId]();
  } else {
    console.log(
      `No action defined for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
    );
  }
}

var scenes = {
  scene0: {
    actions: [
{
      "vending-machine": () => completeScene("vendingMachine", showVendingMachine)
},

    ],
  },
  vendingMachine: {
    actions: [
      {
        // Subscene 1
        pocket: () =>
          moveToSubScene(
            1,
            moveVendingMachineX,
            displayMessage("Take the coin")
          ),
        coin: () => displayMessage("Look somewhere else"), // No action
        ball: () => displayMessage("You need a coin"), // No action
      },
      {
        // Subscene 2
        pocket: () => {}, // No action
        coin: () => moveToSubScene(2, showBall1),
        ball: () => resetToSubScene(0),
      },
      {
        // Subscene 3
        pocket: () => {}, // No action
        coin: () => {},
        ball: () => completeScene("ball1", hideVendingMachine),
      },
    ],
  },
  ball1: {
    actions: [
      {
        // Subscene 1
        boat: () => completeScene("boat", hideBall1, showBoat),
      },
    ],
  },
  boat: {
    actions: [
      {
        // Subscene 1
        bush: () => completeScene("bush", hideBoat, showBush),
      },
    ],
  },
  bush: {
    actions: [
      {
        // Subscene 1
        one: () => {},
        two: () => moveToSubScene(1, showTwo0, moveBushX),
        three: () => {},
      },
      {
        // Subscene 2
        one: () => resetToSubScene(0),
        two: () => {},
        three: () => moveToSubScene(2, showThree0, moveBushZ),
      },
      {
        // Subscene 3
        one: () => completeScene("shop", showOne0, hideBush, showShop, hideThree0, hideTwo0, hideOne0),
        two: () => resetToSubScene(0),
        three: () => {},
      },
    ],
  },

  shop: {
    actions: [
      {
               shop: () => moveToSubScene(1),
      },
      {
        // Subscene 1
        one: () => showOne(),
        two: () => showTwo(),
        three: () => showThree(),
        four: () => moveToSubScene(2, showFour),
      },
      {
        clock: () => completeScene("clock", hideShop, showClock),
      },
    ],
  },

  clock: {
    actions: [
      {
        // Subscene 1
        one: () => {},
        two: () => {},
        three: () => moveToSubScene(1, moveClockX),
        four: () => {},
      },
      {
        // Subscene 2
        one: () => resetToSubScene(0),
        two: () => moveToSubScene(2, moveClockZ),
        three: () => {},
        four: () => resetToSubScene(0),
      },
      {
        // Subscene 3
        one: () => moveToSubScene(3, moveClockX1), // Assuming moveClockX1 is a correct function
        two: () => {},
        three: () => resetToSubScene(0),
        four: () => resetToSubScene(0),
      },
      {
        // Subscene 4
        // Assuming you need an action here. Replace "nextScene" with the correct scene ID and ensure functions exist
        one: () => {},
        two: () => resetToSubScene(0),
        three: () => resetToSubScene(0),
        four: () => completeScene("chest", hideClock, showChest), // Adjust this line accordingly
      },
    ],
  },
  chest: {
    actions: [
      {
        // Subscene 1
        one: () => moveToSubScene(1),
        two: () => {},
        three: () => {},
      },
      {
        // Subscene 2
        one: () => {},
        two: () => moveToSubScene(2),
        three: () => resetToSubScene(0),
      },
      {
        // Subscene 3
        one: () => resetToSubScene(1),
        two: () => {},
        three: () => moveToSubScene(3, hideChest, showTicket),
      },
      {
        // Subscene 3
        ticket: () => completeScene("bus", hideTicket, showBus),
        
      },
    ],
  },
bus: {
    actions: [
      {
        // Subscene 1
        one: () => {},
        two: () => {},
        three: () => {},
      },
      {
        // Subscene 2
        one: () => {},
        two: () => {},
        three: () => {},
      },
      {
        // Subscene 3
        one: () => {},
        two: () => {},
        three: () => {},
      },
      
    ],
  },

  // Ensure other scenes are correctly defined...
};


function showBall1() {
  let ball1 = document.querySelector("#ball1");
  if (ball1) {
    ball1.setAttribute("visible", true);
  } else {
    console.error("Element #ball1 not found");
  }
}

function hideBall1() {
  let ball1 = document.querySelector("#ball1");
  if (ball1) {
    ball1.setAttribute("visible", false);
  } else {
    console.error("Element #ball1 not found");
  }
}
function showVendingMachine() {
  document.querySelector("#vending-machine-3d").setAttribute("visible", true);
}

function hideVendingMachine() {
  document.querySelector("#vending-machine-3d").setAttribute("visible", false);
}


function showBoat() {
  document.querySelector("#boat1").setAttribute("visible", true);
}

function hideBoat() {
  document.querySelector("#boat1").setAttribute("visible", false);
}
function showBush() {
  document.querySelector("#bush1").setAttribute("visible", true);
}
function hideBush() {
  document.querySelector("#bush1").setAttribute("visible", false);
}
function showShop() {
  document.querySelector("#shop1").setAttribute("visible", true);
}
function hideShop() {
  var shopElement = document.querySelector("#shop1"); // Make sure the ID matches the one in your HTML
  if (shopElement) {
    shopElement.setAttribute("visible", "false"); // Ensure we're setting the attribute to a string value
  } else {
    console.error("Shop element not found");
  }
}

function showOne0() {
  document.querySelector("#one0").setAttribute("visible", true);
}
function showTwo0() {
  document.querySelector("#two0").setAttribute("visible", true);
}
function showThree0() {
  document.querySelector("#three0").setAttribute("visible", true);
}

function hideOne0() {
  document.querySelector("#one0").setAttribute("visible", false);
}
function hideTwo0() {
  document.querySelector("#two0").setAttribute("visible", false);
}
function hideThree0() {
  document.querySelector("#three0").setAttribute("visible", false);
}


function showOne() {
  document.querySelector("#one1").setAttribute("visible", true);
}
function showTwo() {
  document.querySelector("#two1").setAttribute("visible", true);
}
function showThree() {
  document.querySelector("#three1").setAttribute("visible", true);
}
function showFour() {
  document.querySelector("#four1").setAttribute("visible", true);
}
function showClock() {
  var clockElement = document.querySelector("#clock1"); // Again, ensure the ID matches your 3D model in HTML
  if (clockElement) {
    clockElement.setAttribute("visible", "true");
  } else {
    console.error("Clock element not found");
  }
}
function hideClock() {
  document.querySelector("#clock1").setAttribute("visible", false);
}
function showChest() {
  document.querySelector("#chest1").setAttribute("visible", true);
}
function hideChest() {
  document.querySelector("#chest1").setAttribute("visible", false);
}
function showTicket() {
  document.querySelector("#ticket1").setAttribute("visible", true);
}
function hideTicket() {
  document.querySelector("#ticket1").setAttribute("visible", false);
}
function showBus() {
  document.querySelector("#bus1").setAttribute("visible", true);
}
function hideBus() {
  document.querySelector("#bus1").setAttribute("visible", false);
}
function moveVendingMachineX() {
  // Hide "vending-machine-3d"
  let vendingMachine = document.querySelector("#vending-machine-3d");
  if (vendingMachine) {
    vendingMachine.setAttribute("visible", false);
  }

  // Move and show "vending-machine-3d2"
  let vendingMachine2 = document.querySelector("#vending-machine-3d2");
  if (vendingMachine2) {
    let currentPosition = vendingMachine2.getAttribute("position");
    vendingMachine2.setAttribute("position", {
      x: currentPosition.x + 1,
      y: currentPosition.y,
      z: currentPosition.z,
    });
    vendingMachine2.setAttribute("visible", true);
  }
}


function moveVendingMachineZ() {
  let vendingMachine = document.querySelector("#vending-machine-3d");
  let currentPosition = vendingMachine.getAttribute("position");
  vendingMachine.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}

function moveBushX() {
  let Bush = document.querySelector("#bush1");
  let currentPosition = Bush.getAttribute("position");
  Bush.setAttribute("position", {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveBushZ() {
  let Bush = document.querySelector("#bush1");
  let currentPosition = Bush.getAttribute("position");
  Bush.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveClockX() {
  let clock = document.querySelector("#clock1");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveClockX1() {
  let clock = document.querySelector("#clock1");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x - 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveClockZ() {
  let clock = document.querySelector("#clock1");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveClockZ1() {
  let clock = document.querySelector("#clock1");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z + 1,
  });
}
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;
  resetVendingMachinePosition(); // Always reset the vending machine position

  // Only reset the bush position if in the bush scene and resetting to the initial subscene
  if (currentScene === "bush" && newIndex === 0) {
    resetBushPosition();
  }
  if (currentScene === "clock" && newIndex === 0) {
    resetClockPosition();
  }
  // Add additional conditions here if there are specific behaviors for other scenes or subscenes
}

function resetVendingMachinePosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#vending-machine-3d")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}

function resetBushPosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#bush1")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function resetClockPosition() {
  // Reset cylinder position to the start position
  document
    .querySelector("#clock1")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function completeScene(nextScene, ...actions) {
  console.log(`Completing scene: ${currentScene}, moving to: ${nextScene}`);
  currentScene = nextScene;
  subSceneIndex = 0;
  actions.forEach((action) => action());
}

function displayMessage(text) {
  const messageElement = document.getElementById("message");
  if (messageElement) {
    messageElement.innerText = text;
    messageElement.style.display = "block"; // Make sure it's visible

    // Hide the message after 10 seconds
    setTimeout(() => {
      messageElement.style.display = "none";
    }, 2000); // 2000 milliseconds = 2 seconds
  } else {
    console.error("Message element not found");
  }
}

function resetScene() {
  subSceneIndex = 0;
  resetVendingMachinePosition();
  resetBushPosition();
  resetClockPosition();
  // Reset other objects if necessary
}

function resetGame() {
  document.querySelector("#boat1").setAttribute("visible", false); // Explicitly hide the cube
  document.querySelector("#bush1").setAttribute("visible", false);
  document.querySelector("#shop1").setAttribute("visible", false);
 document.querySelector("#vending-machine").setAttribute("visible", false);
  document.querySelector("#ball1").setAttribute("visible", false); // Hide skrituldelis initially
  document.querySelector("#one1").setAttribute("visible", false);
  document.querySelector("#two1").setAttribute("visible", false);
  document.querySelector("#three1").setAttribute("visible", false);
  document.querySelector("#four1").setAttribute("visible", false);
  document.querySelector("#clock1").setAttribute("visible", false);
  document.querySelector("#chest1").setAttribute("visible", false);
  document.querySelector("#ticket1").setAttribute("visible", false);
  document.querySelector("#bus1").setAttribute("visible", false);
  resetVendingMachinePosition();
  resetBushPosition();
  resetClockPosition();

  // Reset the scene and subscene indexes
  currentScene = "scene0";
  subSceneIndex = 0;

  // Reset any other game states as needed
}

// A-Frame Component for Continuous Tracking
AFRAME.registerComponent("continuous-tracking", {
  tick: function () {
    // Optional: Continuous tracking logic
  },
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
