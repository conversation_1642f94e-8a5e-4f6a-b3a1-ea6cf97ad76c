// Marker Definitions
var actionMarkers = {
  wallet: { element: document.querySelector("#wallet"), visible: false },
  coin: { element: document.querySelector("#coin"), visible: false },
  ball: { element: document.querySelector("#ball"), visible: false }, 
  boat: { element: document.querySelector("#boat"), visible: false },
  bush: { element: document.querySelector("#bush"), visible: false },
  one: { element: document.querySelector("#one"), visible: false },
  two: { element: document.querySelector("#two"), visible: false },
  three: { element: document.querySelector("#three"), visible: false },
  four: { element: document.querySelector("#four"), visible: false },
  shop: { element: document.querySelector("#shop"), visible: false },
  clock: { element: document.querySelector("#clock"), visible: false },
  chest: { element: document.querySelector("#chest"), visible: false },
  ticket: { element: document.querySelector("#ticket"), visible: false },
  bus: { element: document.querySelector("#bus"), visible: false },
  vendingmachine: { element: document.querySelector("#vendingmachine"), visible: false }
};


var currentScene = "scene0";
var subSceneIndex = 0;
var animationStates = {};
// var playAnimation = false;


// Initialize the game
resetGame();

// Event Listeners for Markers
Object.values(actionMarkers).forEach((marker) => {
  marker.element.addEventListener("markerFound", () => {
    marker.visible = true;
    handleMarkerInteraction(marker.element.id);
  });
  marker.element.addEventListener("markerLost", () => {
    marker.visible = false;
  });
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
  console.log(
    `Handling interaction for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
  );
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    console.log(`Executing action for marker: ${markerId}`);
    currentActions[markerId]();
  } else {
    console.log(
      `No action defined for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
    );
  }
}

var scenes = {
  scene0: { 
    actions: [

      { 
        vendingmachine: () => {
          setEntityVisibility("vending-entity", true),
          setEntityVisibility("wallet-entity", true),
          displayMessage("Get the ball!"),
          completeScene("vendingMachineScene")
        },
      },
    ],
  },
  vendingMachineScene: {
    actions: [
      {
        // Subscene 1
        vendingmachine: () => { },
        wallet: () => {
          playAnimation("maks parādās", "wallet"),
          playAnimation("moneta paradas", "vendingmachine"),
          displayMessage("Take the coin"),
          setEntityVisibility("coin-entity", true),
          setTimeout(() => {
            moveToSubScene(1)
          }, 1000);
        },
        coin: () => displayMessage("Look somewhere else"), // No action
        ball: () => displayMessage("You need a coin"), // No action
      },
      {
  // Subscene 2
        // vendingmachine: () => {},
  wallet: () => {},
  coin: () => {
    playAnimation("monēta pazūd", "wallet"); // Play disappearing animation for wallet
    playAnimation("moneta paradas", "coin"); // Then play the coin appearing animation
    playAnimation("moneta paradas", "vendingmachine"); // Ensure vending machine animation plays
    setEntityVisibility("ball-entity", true); // Make sure ball is visible if needed here or earlier
    moveToSubScene(2);
  },
  ball: () => displayMessage("You need a coin") // No action
},

      
      {
  // Subscene 3
  coin: () => {},
            vendingmachine: () => {
        playAnimation("a", "vendingmachine")
        },
    
  ball: () => {
    playAnimation("moneta pazud", "coin"); // Coin disappearing
    playAnimation("bumba parādās", "ball"); // Ball appears
    playAnimation("a", "vendingmachine"); // Vending machine action
    playAnimation("maks pazūd", "wallet");
    setEntityVisibility("boat-entity", true)// Wallet disappearing
    displayMessage("Place ball marker next to location - Boat")
    completeScene("ballScene")
  },
      },
    ],
  },
  ballScene: {
    actions: [
      {
        // Subscene 1
        boat: () => {completeScene("boatScene", hideBallScene, showBoat),
        setEntityVisibility("wallet-entity", false),
        setEntityVisibility("coin-entity", false),
        setEntityVisibility("vending-entity", false),
        playAnimation("bumba pazūd", "ball"),
        playAnimation("laiva paradas", "boat"),
        displayMessage("Move the boat down the river to the forest location")
  
        },
      },
    ],
  },
  boatScene: {
    actions: [
      {
        // Subscene 1
        bush: () => {completeScene("bushScene", hideBoat, showBush),
         setEntityVisibility("bush-entity", true),
        playAnimation("laiva pazud", "boat"),
         setEntityVisibility("boat-entity", false),
        setEntityVisibility("ball-entity", false),
         playAnimation("bumba paradas", "bush")
         displayMessage("Help the ball find its way out of bushes")
        }
      },
    ],
  },
  bushScene: {
    actions: [
      {
        // Subscene 1
        one: () => {},
        two: () => {moveToSubScene(1,),
                    playAnimation("lec1", "bush")
      },
        three: () => {},
      },
      {
        // Subscene 2
        one: () => resetToSubScene(0),
        two: () => {},
        three: () => {moveToSubScene(2),
                     playAnimation("lec2", "bush")
      }
      },
      {
        // Subscene 3
        one: () =>
          {completeScene(
            "shopScene",
            hideBush,
            showShop,
          ),
          playAnimation("bumba izlec", "bush"), 
          setEntityVisibility("shop-entity", true)
          displayMessage("Place ball marker next to location - Shop")
      },
        two: () => resetToSubScene(0),
        three: () => {},
      },
    ],
  },

  shopScene: {
    actions: [
      {
        shop: () => {moveToSubScene(1),
                     playAnimation("bumba ielec", "bush"),
                     setEntityVisibility("bush-entity", false),
                     setEntityVisibility("hourglass1-entity", true),
                     setEntityVisibility("hourglass5-entity", true),
                     setEntityVisibility("hourglass6-entity", true),
                     setEntityVisibility("hourglass7-entity", true)
                     displayMessage("You have 20sec to analyse hourglasses")
      }
      },
      {
        // Subscene 1
        one: () => {
                    playAnimation("sp paradas", "one"),
                    showOne(),
                    playAnimation("bumba izlec", "shop")
                    
      },
        two: () => {
                    playAnimation("sp paradas", "two"),
                    setEntityVisibility("shop-entity", false),
                    showTwo()
      },
        three: () => {
                      playAnimation("sp paradas", "three"),
                      showThree()
      },
        four: () => {
                     playAnimation("sp paradas", "four"),
                     moveToSubScene(2, showFour)
      }
      },
      {
        // Subscene 2
        one: () => {
                    playAnimation("sp stav", "one")
                    
      },
        two: () => {
                    playAnimation("sp stav", "two")
                    
      },
        three: () => {
                      playAnimation("sp stav", "three")
                    
      },
        four: () => {
                     playAnimation("sp stav", "four"),
                     displayMessage("Analyzing..."),
                     setTimeout(() => {
          moveToSubScene(3);  // Moves to subscene 3 after 20 seconds
        }, 20000);  // 20000 milliseconds = 20 seconds
      }
      },
        {
        // Subscene 3
        one: () => {
                    playAnimation("sp pazud", "one")
                    
      },
        two: () => {
                    playAnimation("sp pazud", "two")
                    
      },
        three: () => {
                      playAnimation("sp pazud", "three")
                     
      },
        four: () => {
                     playAnimation("sp pazud", "four")
                     moveToSubScene(4),
                     setEntityVisibility("clock-brockenA-entity", true)
                     displayMessage("Analyzing complete. Scan clock marker")
                     
                     
      }
      },
        // Subscene 4
      {
        clock: () => {
                     playAnimation("pulkstenis saplist", "clock"),
                     setEntityVisibility("hourglass1-entity", false),
                     setEntityVisibility("hourglass5-entity", false),
                     setEntityVisibility("hourglass6-entity", false),
                     setEntityVisibility("hourglass7-entity", false),
                    displayMessage("The correct time is __:__")
                     completeScene("clockScene", hideShop, showClock)
                     
      },
      },
    ],
  },

  clockScene: {
    actions: [
      {
        // Subscene 1
        one: () => {},
        two: () => {},
        three: () => {moveToSubScene(1, moveClockX),
                     displayMessage("The correct time is 1_:__")
                     setEntityVisibility("clock-brocken-entity", true),
                     setEntityVisibility("clock-brockenA-entity", false)
                     },
        four: () => {},
      },
      {
        // Subscene 2
        one: () => {},
        two: () => {moveToSubScene(2, moveClockZ)
                    displayMessage("The correct time is 16:__")
                    setEntityVisibility("clock-brocken-entity", false),
                    setEntityVisibility("clock-handle1-entity", true)
                   },
        three: () => {},
        four: () => {},
      },
      {
        // Subscene 3
        one: () => {moveToSubScene(3, moveClockX1)
                    displayMessage("The correct time is 16:5_")
                    setEntityVisibility("clock-handle1-entity", false),
                    setEntityVisibility("clock-handle2-entity", true)
                   }, 
        two: () => {},
        three: () => {},
        four: () => {},
      },
      {
        // Subscene 4
        // Assuming you need an action here. Replace "nextScene" with the correct scene ID and ensure functions exist
        one: () => {},
        two: () => {},
        three: () => {},
        four: () => {setEntityVisibility("clock-handle2-entity", false),
                     setEntityVisibility("clock-ticking-entity", true),
                     displayMessage("Congrats! You fixed the clock. The correct time is 16:57"),
                     playAnimation("pulkstenis iet", "clock"),
                     setEntityVisibility("chest-entity", true),
                     moveToSubScene(4)
                     
      },
                     // Adjust this line accordingly
      },
      {
        // Subscene 5
       chest:() => {setEntityVisibility("clock-ticking-entity", false),
                   setEntityVisibility("chest-entity", true),
                   playAnimation("lade paradas", "chest"),
                    
                  completeScene("chestScene", hideClock, showChest)
      },
                   
                   
      },
    ],
  },
  chestScene: {
    actions: [
      {
        chest:() => {setEntityVisibility("chest-entity", true),
                     setEntityVisibility("key1-entity", true),
                     setEntityVisibility("key2-entity", true),
                     setEntityVisibility("key3-entity", true),
                     playAnimation("atslegaF paradas", "key1")
                     playAnimation("atslegaF paradas", "key2")
                     playAnimation("atslegaF paradas", "key3")
                     playAnimation("lade stav", "chest")
                     displayMessage("Connect key fragments in right order")
                     moveToSubScene(1)
      },
      },
      {
        // Subscene 2
        one: () => {moveToSubScene(2)
                    setEntityVisibility("key1-entity", true)},
        two: () => {},
        three: () => {},
      },
      {
        // Subscene 3
        one: () => {},
        two: () => {moveToSubScene(3)
                    setEntityVisibility("key2-entity", true)},
        three: () => resetToSubScene(0),
      },
      {
        // Subscene 4
        one: () => resetToSubScene(4),
        two: () => {},
        three: () => {moveToSubScene(3, hideChest, showTicket),
                    setEntityVisibility("key3-entity", true),
                    setEntityVisibility("chest-entity", true),
                    playAnimation("lade atverta", "chest"),
                    setEntityVisibility("ticket-entity", true)
      },
      },
      {
        // Subscene 5
        ticket: () => {setEntityVisibility("key1-entity", false),
                      setEntityVisibility("key2-entity", false),
                      setEntityVisibility("key3-entity", false),
                      setEntityVisibility("bus-entity", true),
                      playAnimation("bilete paradas", "ticket"),
                      playAnimation("lade pazud", "chest"),
                      completeScene("busScene", hideTicket, showBus)},
      },
    ],
  },
  busScene: {
    actions: [
      {
        // Subscene 1
        bus: () => {playAnimation("piebrauc", "bus"),
                    setEntityVisibility("chest-entity", false),
                    playAnimation("bilete pazud", "ticket"),
                    moveToSubScene(1)
                   },
                    
      },
      {
        // Subscene 1
        bus: () => {playAnimation("stav", "bus"),
                    setEntityVisibility("ticket-entity", false),
                    moveToSubScene(2)
                },
      },
      {
        // Subscene 2
        bus: () => {playAnimation("aizbrauc", "bus")
                    completeScene(hideBus)
                  
                },
      },
      {
        // Subscene 3
      
      },
    ],
  },

  // Ensure other scenes are correctly defined...
};


const animationEntities = [
  document.querySelector("#vending-entity"),
  document.querySelector("#wallet-entity"),
  document.querySelector("#coin-entity"),
  document.querySelector("#ball-entity"),
  document.querySelector("#boat-entity"),
  document.querySelector("#bush-entity"),
  document.querySelector("#shop-entity"),
  document.querySelector("#clock-brockenA-entity"),
  document.querySelector("#clock-brocken-entity"),
  document.querySelector("#clock-handle1-entity"),
  document.querySelector("#clock-handle2-entity"),
  document.querySelector("#clock-ticking-entity"),
  document.querySelector("#chest-entity"),
  document.querySelector("#ticket-entity"),
  document.querySelector("#bus-entity"),
  document.querySelector("#hourglass1-entity"),
  document.querySelector("#hourglass5-entity"),
  document.querySelector("#hourglass6-entity"),
  document.querySelector("#hourglass7-entity"),
  document.querySelector("#vending-entity"),
  document.querySelector("#key1-entity"),
  document.querySelector("#key2-entity"),
  document.querySelector("#key3-entity"),
];

function playAnimation(animationName, markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity) {
    // Remove any existing animation-mixer component
    entity.removeAttribute('animation-mixer');

    // Add the animation-mixer component with the specified animation clip
    entity.setAttribute('animation-mixer', {
      clip: animationName,
      loop: 'once',
      clampWhenFinished: true
    });

    // If it's the vending machine, play the animation immediately
    if (markerId === "vending") {
      entity.components["animation-mixer"].playAction();
    }
  }
}

function playMarkerAnimation(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const animationName = getAnimationName(markerId);
    if (animationName) {
      playAnimation(animationName, markerId);
    }
  }
}

function resetMarkerAnimation(markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity && entity.getAttribute("animation-mixer")) {
    entity.removeAttribute("animation-mixer");
  }
}

function getAnimationName(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const actionFunc = currentActions[markerId];
    const actionString = actionFunc.toString();
    const match = actionString.match(/playAnimation\(["'](.+)["']/);
    if (match) {
      return match[1];
    }
  }
  return null;
}

//------------------------------------------------------------------
//  Funkcijas
//------------------------------------------------------------------


function resetAnimationStates() {
  Object.keys(animationStates).forEach((key) => (animationStates[key] = false));
}

function resetAnimationState(markerId) {
  const animationKey = `${currentScene}_${subSceneIndex}_${markerId}`;
  animationStates[animationKey] = false;
}

function setupEventListeners() {
  Object.values(actionMarkers).forEach((marker) => {
    marker.element.addEventListener("markerFound", () => {
      marker.visible = true;
      handleMarkerInteraction(marker.element.id);
    });
  // Handle the vending machine marker separately
  const vendingMarker = actionMarkers.vendingmachine.element;
  vendingMarker.addEventListener("markerFound", () => {
    actionMarkers.vendingmachine.visible = true;
    handleMarkerInteraction("vendingmachine");

    // Check if the marker is scanned in the first subscene
    if (currentScene === "vendingMachineScene" && subSceneIndex === 0) {
      // Trigger the animation for the vending machine
      playAnimation("b", "vending");
    }
  });
    
    marker.element.addEventListener("markerLost", () => {
      marker.visible = false;
    });
  });
}

function showBallScene() {
  let ballEntity = document.querySelector("#ball-model");
  if (ballEntity) {
    ballEntity.setAttribute("visible", true);
  } else {
    console.error("Element #ball-model not found");
  }
}

function hideBallScene() {
  let ballEntity = document.querySelector("#ball-model");
  if (ballEntity) {
    ballEntity.setAttribute("visible", false);
  } else {
    console.error("Element #ball-model not found");
  }
}

function showBoat() {
  document.querySelector("#boat-model").setAttribute("visible", true);
}

function hideBoat() {
  document.querySelector("#boat-model").setAttribute("visible", false);
}
function showBush() {
  document.querySelector("#bush-model").setAttribute("visible", true);
}
function hideBush() {
  document.querySelector("#bush-model").setAttribute("visible", false);
}
function showShop() {
  document.querySelector("#shop-model").setAttribute("visible", true);
}

function hideShop() {
  var shopElement = document.querySelector("#shop-model"); // Make sure the ID matches the one in your HTML
  if (shopElement) {
    shopElement.setAttribute("visible", "false"); // Ensure we're setting the attribute to a string value
  } else {
    console.error("Shop element not found");
  }
}

function showOne() {
  document.querySelector("#hourglass1-model").setAttribute("visible", true);
}
function showTwo() {
  document.querySelector("#hourglass5-model").setAttribute("visible", true);
}
function showThree() {
  document.querySelector("#hourglass6-model").setAttribute("visible", true);
}
function showFour() {
  document.querySelector("#hourglass7-model").setAttribute("visible", true);
}
function showClock() {
  var clockElement = document.querySelector("#clock-brocken-model"); // Again, ensure the ID matches your 3D model in HTML
  if (clockElement) {
    clockElement.setAttribute("visible", "true");
  } else {
    console.error("Clock element not found");
  }
}
function hideClock() {
  document.querySelector("#clock-brocken-model").setAttribute("visible", false);
}
function showChest() {
  document.querySelector("#chest-model").setAttribute("visible", true);
}
function hideChest() {
  document.querySelector("#chest-model").setAttribute("visible", false);
}
function showTicket() {
  document.querySelector("#ticket-model").setAttribute("visible", true);
}
function hideTicket() {
  document.querySelector("#ticket-model").setAttribute("visible", false);
}
function showBus() {
  document.querySelector("#bus-model").setAttribute("visible", true);
}
function hideBus() {
  document.querySelector("#bus-model").setAttribute("visible", false);
}


function moveBushX() {
  let Bush = document.querySelector("#bush-model");
  let currentPosition = Bush.getAttribute("position");
  Bush.setAttribute("position", {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveBushZ() {
  let Bush = document.querySelector("#bush-model");
  let currentPosition = Bush.getAttribute("position");
  Bush.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveClockX() {
  let clock = document.querySelector("#clock-brocken-model");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x + 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveClockX1() {
  let clock = document.querySelector("#clock-brocken-model");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x - 1,
    y: currentPosition.y,
    z: currentPosition.z,
  });
}
function moveClockZ() {
  let clock = document.querySelector("#clock-brocken-model");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z - 1,
  });
}
function moveClockZ1() {
  let clock = document.querySelector("#clock-brocken-model");
  let currentPosition = clock.getAttribute("position");
  clock.setAttribute("position", {
    x: currentPosition.x,
    y: currentPosition.y,
    z: currentPosition.z + 1,
  });
}
function moveToSubScene(newIndex, action) {
  subSceneIndex = newIndex;
  if (action) action();
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;

  if (currentScene === "bushScene" && newIndex === 0) {
    resetBushPosition();
  }
  if (currentScene === "clockScene" && newIndex === 0) {
    resetClockPosition();
  }
}


function resetBushPosition() {
  document
    .querySelector("#bush-model")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function resetClockPosition() {
  document
    .querySelector("#clock-brocken-model")
    .setAttribute("position", { x: 0, y: 0, z: 0 });
}
function completeScene(nextScene, ...actions) {
  console.log(`Completing scene: ${currentScene}, moving to: ${nextScene}`);
  currentScene = nextScene;
  subSceneIndex = 0;
  actions.forEach((action) => action());
}

function displayMessage(text) {
  const messageElement = document.getElementById("message");
  if (messageElement) {
    messageElement.innerText = text;
    messageElement.style.display = "block"; // Make sure it's visible

    setTimeout(() => {
      messageElement.style.display = "none";
    }, 4000); // 2000 milliseconds = 2 seconds
  } else {
    console.error("Message element not found");
  }
}

function setEntityVisibility(entityId, visible) {
  const entity = document.querySelector(`#${entityId}`);
  if (entity) {
    entity.setAttribute("visible", visible);
  } else {
    console.error(`Element #${entityId} not found`);
  }
}

function resetScene() {
  subSceneIndex = 0;
  resetBushPosition();
  resetClockPosition();
}

function resetGame() {
  resetBushPosition();
  resetClockPosition();

  setEntityVisibility("coin-entity", false);
  setEntityVisibility("ball-entity", false); 
  setEntityVisibility("boat-entity", false);
  setEntityVisibility("bush-entity", false); 
  setEntityVisibility("shop-entity", false);
  setEntityVisibility("clock-brockenA-entity", false);
  setEntityVisibility("clock-brocken-entity", false);
  setEntityVisibility("clock-handle1-entity", false);
  setEntityVisibility("clock-handle2-entity", false);
  setEntityVisibility("clock-ticking-entity", false);
  setEntityVisibility("chest-entity", false);
  setEntityVisibility("ticket-entity", false);   
  setEntityVisibility("bus-entity", false);   
  setEntityVisibility("wallet-entity", false);   
  setEntityVisibility("vending-entity", false);
  setEntityVisibility("hourglass1-entity", false);
  setEntityVisibility("hourglass5-entity", false);
  setEntityVisibility("hourglass6-entity", false);
  setEntityVisibility("hourglass7-entity", false);
  setEntityVisibility("key1-entity", false);
  setEntityVisibility("key2-entity", false);
  setEntityVisibility("key3-entity", false);
  currentScene = "scene0";
  subSceneIndex = 0;
  
  
  // const walletEntity = document.querySelector("#wallet-entity");
  const vendingEntity = document.querySelector("#vending-entity");
  // walletEntity.removeAttribute('animation-mixer');
  vendingEntity.removeAttribute('animation-mixer');
}


AFRAME.registerComponent("continuous-tracking", {
  tick: function () {
  },
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
