// Marker Definitions
var actionMarkers = {
  wallet: { element: document.querySelector("#wallet"), visible: false },
  coin: { element: document.querySelector("#coin"), visible: false },
  ball: { element: document.querySelector("#ball"), visible: false },
  boat: { element: document.querySelector("#boat"), visible: false },
  bush: { element: document.querySelector("#bush"), visible: false },
  shop: { element: document.querySelector("#shop"), visible: false },
  clock: { element: document.querySelector("#clock"), visible: false },
  chest: { element: document.querySelector("#chest"), visible: false },
  ticket: { element: document.querySelector("#ticket"), visible: false },
  bus: { element: document.querySelector("#bus"), visible: false },
  vm: { element: document.querySelector("#vm"), visible: false },
  bush1: { element: document.querySelector("#bush1"), visible: false },
  bush2: { element: document.querySelector("#bush2"), visible: false },
  bush3: { element: document.querySelector("#bush3"), visible: false },
  hg1: { element: document.querySelector("#hg1"), visible: false },
  hg2: { element: document.querySelector("#hg2"), visible: false },
  hg3: { element: document.querySelector("#hg3"), visible: false },
  hg4: { element: document.querySelector("#hg4"), visible: false },
  key1: { element: document.querySelector("#key1"), visible: false },
  key2: { element: document.querySelector("#key2"), visible: false },
  key3: { element: document.querySelector("#key3"), visible: false },
};

var currentScene = "scene0";
var subSceneIndex = 0;
var animationStates = {};
var isScanning = false;
var timerText = document.getElementById("timerText");
var currentTime = 1260; // Initial time in minutes (18:00)
var recentlyScannedMarkers = {};
var scannedMarkers = {}; // Add this line at the beginning of your script

// Initialize the game
resetGame();

function updateTimer(newTime = null) {
  if (newTime !== null) {
    currentTime = newTime;
  }

  var hours = Math.floor(currentTime / 60);
  var minutes = currentTime % 60;
  var formattedTime =
    hours.toString().padStart(2, "0") +
    ":" +
    minutes.toString().padStart(2, "0");
  timerText.innerText = formattedTime;

  // Apply animation
  timerText.classList.add("time-change");
  setTimeout(() => {
    timerText.classList.remove("time-change");
  }, 500); // Match the duration of the CSS animation
}

function incrementTimer() {
  if (!isScanning) return;
  if (!scannedMarkers[currentScene]) {
    scannedMarkers[currentScene] = {};
  }
  if (scannedMarkers[currentScene][subSceneIndex]) return; // Skip if already scanned

  scannedMarkers[currentScene][subSceneIndex] = true; // Mark as scanned
  currentTime += 15;
  console.log(`Incrementing timer: ${currentTime} minutes`);
  updateTimer();
}

var timerText = document.getElementById("timerText");
if (!timerText) {
  console.error("Timer text element not found");
}

function showStoryContainer() {
  var storyContainer = document.getElementById("storyContainer");
  storyContainer.style.display = "flex";
  storyContainer.classList.remove("hide");
}

function hideStoryContainer() {
  var storyContainer = document.getElementById("storyContainer");
  var storyText = document.getElementById("storyText");
  storyContainer.classList.add("hide");
  storyText.classList.add("hide");
  setTimeout(function () {
    storyContainer.style.display = "none";
    storyContainer.classList.remove("hide");
    storyText.classList.remove("hide");
  }, 500);
}

// Update the scanButton event listener
var scanButton = document.getElementById("scanButton");
scanButton.addEventListener("click", function () {
  if (!isScanning) {
    startScanning();
  }
});

function startScanning() {
  isScanning = true;
  setupEventListeners();

  // Simulate markerFound event for all visible markers
  let markerFound = false;
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  Object.keys(currentActions).forEach((markerId) => {
    if (actionMarkers[markerId].element.object3D.visible) {
      simulateMarkerFound(actionMarkers[markerId].element);
      markerFound = true;
    }
  });

  if (!markerFound && scenes[currentScene].onScan) {
    scenes[currentScene].onScan();
  }

  // Stop scanning after 1 second
  setTimeout(stopScanning, 1000);
}

function stopScanning() {
  console.log("Stop scanning");
  isScanning = false;
  removeEventListeners();
}

function resetScanningState() {
  isScanning = false;
  Object.values(actionMarkers).forEach((marker) => {
    marker.visible = false;
  });
}

function setupEventListeners() {
  Object.values(actionMarkers).forEach((marker) => {
    marker.element.addEventListener("markerFound", markerFoundHandler);
    marker.element.addEventListener("markerLost", markerLostHandler);
  });
}

function removeEventListeners() {
  Object.values(actionMarkers).forEach((marker) => {
    marker.element.removeEventListener("markerFound", markerFoundHandler);
    marker.element.removeEventListener("markerLost", markerLostHandler);
  });
}

function markerFoundHandler(event) {
  if (isScanning) {
    var markerId = event.target.id;
    if (!recentlyScannedMarkers[markerId]) {
      var marker = actionMarkers[markerId];
      marker.visible = true;
      handleMarkerInteraction(marker.element.id);

      recentlyScannedMarkers[markerId] = true;
      setTimeout(() => {
        delete recentlyScannedMarkers[markerId];
      }, 1000); // 1 second debounce time
    }
  }
}

function markerLostHandler(event) {
  var marker = actionMarkers[event.target.id];
  marker.visible = false;
}

// Simulate the markerFound event
function simulateMarkerFound(element) {
  var event = new CustomEvent("markerFound");
  element.dispatchEvent(event);
}

function tellStory(textParts, callback) {
  var storyContainer = document.getElementById("storyContainer");
  var storyText = document.getElementById("storyText");
  var buttonsContainer = document.getElementById("buttonsContainer");
  var backButton = document.getElementById("backButton");
  var nextButton = document.getElementById("nextButton");

  var currentPart = 0;

  function displayTextPart() {
    storyText.innerText = textParts[currentPart];
    backButton.style.display = currentPart > 0 ? "inline-block" : "none";
    nextButton.innerText =
      currentPart < textParts.length - 1 ? "Tālāk" : "Aizvērt";
  }

  function moveToNextPart() {
    if (currentPart < textParts.length - 1) {
      currentPart++;
      displayTextPart();
    } else {
      hideStoryContainer();
      if (callback) {
        setTimeout(function () {
          callback();
        }, 500);
      }
      backButton.removeEventListener("click", moveToPreviousPart);
      nextButton.removeEventListener("click", moveToNextPart);
    }
  }

  function moveToPreviousPart() {
    if (currentPart > 0) {
      currentPart--;
      displayTextPart();
    }
  }

  backButton.addEventListener("click", moveToPreviousPart);
  nextButton.addEventListener("click", moveToNextPart);

  showStoryContainer();
  displayTextPart();
}

function updateSceneImage(imageSrc) {
  var sceneImage = document.getElementById("sceneImage");
  sceneImage.src = imageSrc;
}

function startGame() {
  console.log("Starting game");
  currentScene = "scene0";
  subSceneIndex = 0;
  // Trigger the onStart function of scene0
  if (scenes[currentScene].onStart) {
    scenes[currentScene].onStart();
  }
}

function moveToNextSubscene() {
  console.log("Moving to next subscene");
  const currentActions = scenes[currentScene].actions;

  if (subSceneIndex < currentActions.length - 1) {
    subSceneIndex++;
  } else {
    // If it's the last subscene, move to the next scene
    console.log("Last subscene, moving to next scene");
    moveToNextScene();
  }
}

function moveToNextScene(nextScene) {
  console.log(`Moving to next scene: ${nextScene}`);
  currentScene = nextScene;
  subSceneIndex = 0;
  console.log(`Updating timer before moving to next scene: ${currentScene}`);
  incrementTimer(); // Call incrementTimer instead of updateTimer
}

function moveToSubScene(newIndex, action) {
  console.log(`Moving to subscene: ${newIndex} in scene: ${currentScene}`);
  subSceneIndex = newIndex;
  if (action) action();
  incrementTimer(); // Call incrementTimer instead of updateTimer
  console.log(`Updating timer after moving to subscene: ${subSceneIndex}`);
}

// Event Listeners for Markers
Object.values(actionMarkers).forEach((marker) => {
  marker.element.addEventListener("markerFound", () => {
    marker.visible = true;
    handleMarkerInteraction(marker.element.id);
  });
  marker.element.addEventListener("markerLost", () => {
    marker.visible = false;
  });
});

function handleMarkerInteraction(markerId) {
  if (!isScanning) return;

  console.log(
    `Handling interaction for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
  );

  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    if (!scannedMarkers[currentScene]) {
      scannedMarkers[currentScene] = {};
    }
    if (
      scannedMarkers[currentScene][subSceneIndex] &&
      scannedMarkers[currentScene][subSceneIndex][markerId]
    ) {
      console.log(`Marker ${markerId} already scanned in this subscene`);
      return; // Marker already scanned in this subscene
    }

    console.log(`Executing action for marker: ${markerId}`);
    currentActions[markerId]();
    incrementTimer(); // Increment the timer by 15 minutes

    // Mark this marker as scanned in this subscene
    if (!scannedMarkers[currentScene][subSceneIndex]) {
      scannedMarkers[currentScene][subSceneIndex] = {};
    }
    scannedMarkers[currentScene][subSceneIndex][markerId] = true;
  } else {
    console.log(
      `No action defined for marker: ${markerId} in scene: ${currentScene}, subscene: ${subSceneIndex}`
    );
  }
}

scanButton.addEventListener("click", function () {
  console.log("Scan button clicked");
  if (!isScanning) {
    startScanning();
  }
});

var scenes = {
  scene0: {
    onStart: () => {
      tellStory(
        [
          "Laipni lūdzam spēlē “Lēciens uz priekšu”!",
          "Tu atrodies Venēcijā un seko līdzi gumijas bumbiņai. Izvēlies nepieciešamās kārtis, risini mīklas un atklāj noslēpumus katrā grāmatas lapaspusē.",
          "Vai esi gatavs sākt piedzīvojumu?",
        ],
        () => {
          updateTimer();
          moveToNextScene("scene1");
        }
      );
      updateSceneImage(
        "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079"
      );
    },
  },
  scene1: {
    actions: [
      // Subscene 1
      {
        vm: () => {
          playAnimation("c", "vm");
          setEntityVisibility("vm-entity", true);
          updateTimer();
          setTimeout(() => {
            tellStory(
              [
                "Mūs atstāja vienas! Ir trešā ceļojuma diena. Staigājot pa Venēcijas ielām, ar draudzeni palikām divatā, jo vecāki atgriezās atpakaļ kempingā.",
                "Likās neticami pirmo reizi būt vienām svešā pilsētā, svešā valstī.",
                "Kabatā bija palikušas pēdējās monētas. Kādā šaurā ieliņā pamanījām spēļu automātu, pie kura uzreiz pienācām un intereses pēc izlēmām to izmēģināt.",
              ],
              () => {}
            );
            updateSceneImage(
              "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/vm-inventry.png?v=1716038375712"
            );
            moveToNextScene("vendingMachineScene");
          }, 3000);
        },
      },
    ],
    //         onScan: () => {
    //   displayMessage("Meklē spēļu automāta marķieri, lai turpinātu!");
    // }
  },
  vendingMachineScene: {
    actions: [
      {
        // Subscene 1
        vm: () => {
          // displayMessage("Paņem kārtis, kas norādītas inventāra logā."),
          updateTimer();
          incrementTimer(); // Ensure timer is updated
        },
        wallet: () => {
          setEntityVisibility("wallet-entity", true);
          playAnimation("maks parādās", "wallet"),
            displayMessage("Paņem monētu"),
            // incrementTimer();  // Ensure timer is updated
            updateTimer();
          moveToSubScene(1);
          // setTimeout(() => {
          //   moveToSubScene(1)
          // }, 1500);
        },
        coin: () => {
          displayMessage("Meklē citur"),
            // incrementTimer();  // Ensure timer is updated
            updateTimer();
        }, // No action
        ball: () => {
          displayMessage("Tev nepieciešama monēta"), updateTimer();
          // incrementTimer();  // Ensure timer is updated
        }, // No action
      },
      {
        // Subscene 2
        vm: () => {},
        wallet: () => {},
        coin: () => {
          setEntityVisibility("coin-entity", true),
            playAnimation("monēta pazūd", "wallet"), // Play disappearing animation for wallet
            playAnimation("moneta paradas", "coin"), // Then play the coin appearing animation
            playAnimation("b", "vm"), // Ensure vending machine animation plays
            moveToSubScene(2),
            // incrementTimer();  // Ensure timer is updated
            updateTimer();
        },
        ball: () => {
          // incrementTimer();  // Ensure timer is updated
          updateTimer();
          if (currentTime >= 1305) {
            // Check if the current time is 18:30 or later
            displayMessage("Tev nepieciešama monēta!");
          } else {
            displayMessage("Tev nepieciešama monēta"); // No action
          }
        },
      },
      {
        // Subscene 3

        ball: () => {
          setEntityVisibility("ball-entity", true),
            playAnimation("moneta pazud", "coin"), // Coin disappearing
            playAnimation("bumba paradas", "ball"), // Ball appears
            playAnimation("a", "vm"), // Vending machine action
            playAnimation("maks pazūd", "wallet"),
            // incrementTimer();
            updateTimer();
          setTimeout(() => {
            tellStory(
              [
                "Lecoša gumijas bumbiņa? Tā bija tik muļķīga, ka mēs tai uzreiz pieķērāmies.",
                "Jau tuvojās vakars un attapāmies, ka drīz pienāks pēdējā laiva uz kempingu.",
                "Visu ceļu līdz laivai mētājām bumbiņu. Tā bija tik atsperīga, ka to vairākas reizes gandrīz pazaudējām. ",
              ],
              () => {}
            );
            moveToNextScene("ballScene");
            updateSceneImage(
              "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/ball-inventpry.png?v=1716038728745"
            );
          }, 2500);
        },
      },
    ],
  },
  ballScene: {
    actions: [
      {
        // Subscene 1
        vm: () => {
          displayMessage("Pāršķir nākamo lapu");
        },
        ball: () => {},
        boat: () => {
          playAnimation("laiva paradas bez bumbas", "boat"),
            setEntityVisibility("vm-entity", false),
            setEntityVisibility("boat-entity", true);
          moveToSubScene(1);
        },
      },
      {
        // Subscene 1
        ball: () => {
          // displayMessage("Noliec bumbas kārti blakus laivas pieturai"),
          setEntityVisibility("wallet-entity", false);
          setEntityVisibility("coin-entity", false);
          moveToSubScene(2);
        },
        boat: () => {},
      },
      {
        // Subscene 2

        boat: () => {
          setEntityVisibility("boat-entity", true),
            setEntityVisibility("wallet-entity", false),
            setEntityVisibility("coin-entity", false),
            setEntityVisibility("vm-entity", false),
            playAnimation("bumba pazud", "ball"),
            playAnimation("laiva paradas", "boat"),
            moveToNextScene("boatScene");
          setTimeout(() => {
            tellStory(
              [
                "Beidzot var apsēsties!",
                "Braucot laivā uznāca nogurums no staigāšanas, un atcerējos par garo nogurdinošo pargājienu mežā, kur paliku gulēt teltī.",
                "Tas bija biedējoši…BUMBIŅA RIPO PROM! Gandrīz iekrita ūdeni!",
              ],
              () => {}
            );
          }, 3000);
        },
      },
    ],
  },
  boatScene: {
    actions: [
      {
        // Subscene 1
        boat: () => {
          displayMessage("Pāršķir nākamo lapu");
        },
        bush: () => {
          setEntityVisibility("bush-entity", true),
            setEntityVisibility("ball-entity", false),
            playAnimation("laiva pazud", "boat"),
            playAnimation("bumba paradas", "bush"),
            moveToNextScene("bushScene");
          updateTimer(60);
          setTimeout(() => {
            tellStory(
              [
                "Naktis ir tik siltas! Kempingā ieradāmies ar tumsu. Bija jau viens naktī. Vecāki jau gulēja, bet mēs izlēmām iziet vakara pastaigā pa kempingu.",
                "Turpat parkā mētājām bumbiņu pāri krūmam, pēkšņi tā atsitās pret draudzenes kroksi un ieripoja krūmā. Turpmāko 30 minūšu laika meklējām bumbiņu krūmos, kamēr pie mums nepienāca apsargs…",
                "Uz brīdi likās, ka būs nepatikšanas, bet viņš mums palīdzēja meklējumos ar savu spilgto lukturīti.",
              ],
              () => {}
            );
            updateSceneImage(
              "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/bush-inventory.png?v=1716038374482"
            );
          }, 3000);
        },
      },
    ],
  },
  bushScene: {
    actions: [
      {
        // Subscene 0
        bush: () => {
          moveToSubScene(1),
            displayMessage("Palīdzi bumbai atrast ceļu ārā no krūmiem");
        },
        bush2: () => {
          moveToSubScene(2), playAnimation("lec1", "bush");
        },
      },
      {
        // Subscene 1
        bush: () => {},
        bush1: () => {
          displayMessage("Šajā krūmā bumbiņa nav");
        },
        bush2: () => {
          moveToSubScene(2), playAnimation("lec1", "bush");
        },
        bush3: () => {
          displayMessage("Šajā krūmā bumbiņa nav");
        },
      },
      {
        // Subscene 2
        bush: () => {
          setEntityVisibility("boat-entity", false); //šo pārcēlu te, lai laiva varētu atspēlēt pazušanas animāciju iepriekšējā ainā
        },
        bush1: () => {
          displayMessage("Bumbiņa te nav");
        },
        bush2: () => {
          displayMessage("Bumbiņa te vairs nav");
        },
        bush3: () => {
          moveToSubScene(3), playAnimation("lec2", "bush");
        },
      },
      {
        // Subscene 3
        bush1: () => {
          playAnimation("bumba izlec", "bush"), moveToNextScene("shopScene");
          setTimeout(() => {
            tellStory(
              [
                "Atradās! Beidzot varējām iet gulēt, un apsargs – turpināt savu darbu.",
                "Galvenais, rīt neaizmirst nopirkt suvenīrus…",
              ],
              () => {}
            );
            updateSceneImage(
              "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079"
            );
            updateTimer(840);
          }, 6000);
        },
        bush2: () => {},
        bush3: () => {},
      },
    ],
  },

  shopScene: {
    actions: [
      {
        bush: () => {
          displayMessage("Pāršķir nākamo lapu");
        },
        shop: () => {
          setEntityVisibility("shop-entity", true),
            playAnimation("bumba ielec", "shop"),
            setEntityVisibility("bush-entity", false),
            // displayMessage("You have 20sec to analyse hourglasses"),
            setTimeout(() => {
              tellStory(
                [
                  "Pēdējā diena Venēcijā! Gājām pirkt suvenīrus. Tā man vienmēr ir vienmēr bijusi atbildīgākā ceļojuma daļa. Mājās gaida divas ģimenes un draugi, kuriem kaut kas jāatved no ceļojuma.",
                  "Ilgi staigājām pa veikalu un iepirkumu grozs palika smagāks un smagāks, līdz atcerējāmies, ka veikals kempingā veras ciet jau dienas vidū.",
                  "Palika trīs minūtes. Aizmirsu par suvenīru omei, un kur palika bumbiņa?!",
                ],
                () => {}
              );
              moveToSubScene(1);
              updateSceneImage(
                "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/gh-inventory.png?v=1716038374951"
              );
            }, 7000);
        },
      },
      {
        //sub1
        shop: () => {
          displayMessage(
            "Pēc 5. minūtēm veikals verās ciet – saliec pulksteņus pareizā secībā!"
          );
          setEntityVisibility("hg1-entity", true),
            setEntityVisibility("hg2-entity", true),
            setEntityVisibility("hg3-entity", true),
            setEntityVisibility("hg4-entity", true),
            playAnimation("sp paradas", "hg1");
          playAnimation("sp paradas", "hg2");
          playAnimation("sp paradas", "hg3");
          playAnimation("sp paradas", "hg4");
          moveToSubScene(2);
        },
      },

      //sub2
      {
        shop: () => {},
        hg1: () => {
          moveToSubScene(2);
        },
        hg2: () => {
          moveToSubScene(2);
        },
        hg3: () => {
          displayMessage("Četras minūtes...");
          moveToSubScene(3);
                    updateTimer(841);
        },
        hg4: () => {
          moveToSubScene(2);
        },
      },

      //sub3
      {
        shop: () => {},
        hg1: () => {
          moveToSubScene(3);
        },
        hg2: () => {
          moveToSubScene(3);
        },
        hg3: () => {
          moveToSubScene(3);
        },
        hg4: () => {
          displayMessage("Trīs minūtes...");
          moveToSubScene(4);
                    updateTimer(842);
        },
      },
      {
        shop: () => {},
        hg1: () => {
          moveToSubScene(4);
        },
        hg2: () => {
          displayMessage("Divas minūtes...");
          moveToSubScene(5);
                    updateTimer(843);

        },
        hg3: () => {
          moveToSubScene(4);
        },
        hg4: () => {
          moveToSubScene(4);
        },
      },
      {
        shop: () => {},
        hg1: () => {
                    updateTimer(844);
          displayMessage("Viena minūte...");
          moveToSubScene(6);
        },
        hg2: () => {
          moveToSubScene(5);
        },
        hg3: () => {
          moveToSubScene(5);
        },
        hg4: () => {
          moveToSubScene(5);
        },
      },

      {
        shop: () => {
                    updateTimer(942);
          displayMessage("Veikalu ver ciet");
          moveToNextScene("hgScene");
        },
      },
    ],
  },
  hgScene: {
    actions: [
      {
        shop: () => {
          displayMessage("Pāršķir nākamo lapu");
        },


        clock: () => {
          setEntityVisibility("clock-brockenA-entity", true),
            playAnimation("pulkstenis saplist", "clock"),
            moveToNextScene("clockScene");
            playAnimation("sp stav", "hg1");
             playAnimation("sp stav", "hg2");
            playAnimation("sp stav", "hg3");
            playAnimation("sp stav", "hg4");
          updateTimer(943);
          setTimeout(() => {
            tellStory(
              [
                "Vai pulkstenis ir saplīsis?",
                "Bumbiņa bija izripojusi ārā no veikala uz lielā pulksteņa pusi. Apsēdāmies uz soliņa apskatīt nopirkto un parunāt.",
                "Pēc kāda laika apskatījām arī lielo pulksteni un bijām apmulsumā, kādēļ tas stāv un neiet uz priekšu. Telefoni mums bija izlādējušies, tādēļ cerējām, ka lielā pulkstena laiks nav pareizs, jo tas nozīmētu, ka mēs kavējam autobusu…",
              ],
              () => {}
            );
            //  updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079');
            // updateTimer(840);
          }, 4000);
        },
      },
    ],
  },

  clockScene: {
    actions: [
      {
        clock: () => displayMessage("Pareizs laiks ir __:__"),
        // Subscene 1
        hg1: () => {
                    moveToSubScene(0);
        },
        hg2: () => {
                    moveToSubScene(0);
        },
        hg3: () => {
          moveToSubScene(1), updateTimer(958);
          displayMessage("Pareizs laiks ir 1_:__"),
            setEntityVisibility("clock-brocken-entity", true),
            setEntityVisibility("clock-brockenA-entity", false);
        },
        hg4: () => {
                    moveToSubScene(0);
        },
      },
      {
        // Subscene 2
        hg1: () => {
                    moveToSubScene(1);
        },
        hg2: () => {
          moveToSubScene(2), updateTimer(973);
          displayMessage("Pareizs laiks ir 16:__"),
            setEntityVisibility("clock-brocken-entity", false),
            setEntityVisibility("clock-handle1-entity", true);
        },
        hg3: () => {
                    moveToSubScene(1);
        },
        hg4: () => {
                    moveToSubScene(1);
        },
      },
      {
        // Subscene 3
        hg1: () => {
          moveToSubScene(3), updateTimer(988);
          displayMessage("Pareizs laiks ir 16:5_"),
            setEntityVisibility("clock-handle1-entity", false),
            setEntityVisibility("clock-handle2-entity", true);
        },
        hg2: () => {
                    moveToSubScene(2);
        },
        hg3: () => {
                    moveToSubScene(2);
        },
        hg4: () => {
                    moveToSubScene(2);
        },
      },
      {
        // Subscene 4
        // Assuming you need an action here. Replace "nextScene" with the correct scene ID and ensure functions exist
        hg1: () => {
                    moveToSubScene(3);
        },
        hg2: () => {
                    moveToSubScene(3);
        },
        hg3: () => {
                    moveToSubScene(3);
        },
        hg4: () => {
          updateTimer(1002);
          setEntityVisibility("clock-handle2-entity", false),
            setEntityVisibility("clock-ticking-entity", true),
            displayMessage(
              "Apsveicu! Tu salaboji puksteni. Pareizs laiks ir 16:57"
            ),
            playAnimation("pulkstenis iet", "clock"),
            moveToNextScene("chestzeroScene");
          playAnimation("sp pazud", "hg1"),
            playAnimation("sp pazud", "hg2"),
            playAnimation("sp pazud", "hg3"),
            playAnimation("sp pazud", "hg4"),
          setTimeout(() => {
            tellStory(
              [
                "Pēc ilgas sēdēšanas uz soliņa izdomājām tomēr doties mājās, ja nu tiešām ir tik vēls cik rādīja pulkstenī, jāpaspēj sakrāmēt vēl čemodāni. ",
              ],
              () => {}
            );
            //  updateSceneImage('https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079');
            updateTimer(1065);
          }, 4000);
        },
      },
    ],
  },
  chestzeroScene: {
    actions: [
      {
        // Subscene 5
        clock: () =>
        {
          displayMessage("Pāršķir nākamo lapu")
        },
        chest: () => {
          setEntityVisibility("chest-entity", true),
          setEntityVisibility("clock-ticking-entity", false),
          playAnimation("lade paradas", "chest"),
          moveToNextScene("chestScene");
          setTimeout(() => {
            tellStory(
              [
                "Mēs aizmirsām kodu! Atnākot atpakaļ sapratām, ka mēs tik tiešām kavējam autobusu un vecāki jau aizbrauca! Ātri sakrāmējām somas.",
                "Elektroniskās biļetes mums nebija pieejamas, jo telefons bija izlādējies. Papīra biļetes laikam atradās čemodānā, čemodānā ar kodu…Kāds bija kods un kur atkal palika bumbiņa??",
              ],
              () => {}
            );
            updateSceneImage(
              "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/TUKS.png?v=1715169637079"
            );
            // updateTimer(840);
          }, 4000);

          updateSceneImage(
            "https://cdn.glitch.global/d202b7c0-0128-4708-8d12-a2ad5a1527ef/KEY_INVENTORY.png?v=1716208711141"
          );
        },
      },
    ],
  },
  chestScene: {
    actions: [
      {
        chest: () => {
          setEntityVisibility("key1-entity", true),
            setEntityVisibility("key2-entity", true),
            setEntityVisibility("key3-entity", true),
            playAnimation("atslegaF paradas", "key1"),
            playAnimation("atslegaF paradas", "key2"),
            playAnimation("atslegaF paradas", "key3"),
            playAnimation("lade stav", "chest"),
            displayMessage("Savieno atslēgas fragmentus, lai atvērtu lādi"),
            moveToSubScene(1);
        },
      },
      {
        // Subscene 1
                chest: () => {},
        key1: () => {
        },
        key2: () => {
          //animācija ka parādās viena atslēga
                    displayMessage("Ieliec atslēgu lādē");
                   setEntityVisibility("key1-entity", false);
                   setEntityVisibility("key3-entity", false);
                   moveToSubScene(2)
        },
        key3: () => {
        },
      },
      {
        // Subscene 2
        chest: () => {},
        key2: () => {
          moveToSubScene(3),
            setEntityVisibility("key2-entity", false);
            playAnimation("lade atverta", "chest");
        },
      },
      {
        // Subscene 3
        chest: () => {},
        ticket: () => {
          setEntityVisibility("ticket-entity", true);
          setEntityVisibility("key1-entity", false),
            setEntityVisibility("key2-entity", false),
            setEntityVisibility("key3-entity", false),
            playAnimation("bilete paradas", "ticket"),
            playAnimation("lade pazud", "chest"),
            moveToNextScene("buszeroScene");
          tellStory(
            [
              "Pārmeklējām visas pārējās somas ar cerību, ka biļetes nav čemodānā ar kodu.",
              "Vienā no somām atradām bumbiņu, kuru netīšām iemetām kopā ar visām mantām, bet tomēr biļetes atradās čemodānā ar kodu.",
              "Beigās dabujām vaļā čemodānu un atradām biļetes, bet bija jātiek vēl līdz autobusam, mums bija trīs opcijas, braukt ar skūteri, iet ar kājām vai izsaukt taksi.",
            ],
            () => {}
          );
        },
      },
    ],
  },
  buszeroScene: {
    actions: [
      {
        chest: () => {
          displayMessage("Pāršķir nākamo lapu")
        },
        bus: () => {
          setEntityVisibility("bus-entity", true),
            playAnimation("piebrauc", "bus"),
            setEntityVisibility("chest-entity", false),
            playAnimation("bilete pazud", "ticket"),
            moveToNextScene("busScene");
          tellStory(
            [
              "Mēs paspējām! Tikām līdz autobusam, kur mūs sagaidīja saraizējušies vecāki. Iekāpām autobusā un sākām ceļu uz lidostas pusi.",
            ],
            () => {}
          );
        },
      },
    ],
  },
  busScene: {
    actions: [
      {
        // Subscene 1
        ticket: () => {
          moveToSubScene(1)
        },
        bus: () => {
          playAnimation("stav", "bus"),
          tellStory(
            [
              "Tā arī pienāca ceļojuma beigas. Ceļojums bija piedzīvojumu, prieka un izaicinājumu pilns un daļa no tā bija lecošā gumijas bumbiņa.",
              " Neskatoties uz neskaitāmām reizēm, kad bumbiņa pazuda vai aizripoja, vienmēr atradās risinājums un motivācija to atrast.",
              "Tāpat, kā spēles autores dzīvē, īstermiņa atmiņas problēmas bieži vien ietekmē ikdienas gaidu un sarežģī to. Meklējot sevi un risinot problēmas vienmēr atradīsies cilvēki, kas palīdzēs, atradīsies iekšējā motivācija kaut ko mainīt un cīnīties par labāko.",
              " “Tas vienmēr šķiet neiespējami, kamēr tas nav izdarīts”",
            ],
            () => {}
          );
        },
      },
      {
        // Subscene 2
        bus: () => {
                      setEntityVisibility("ticket-entity", false),
          playAnimation("aizbrauc", "bus"), moveToNextScene("completeScene");
        },
      },
    ],
  },
  completeScene: {
    actions: [
      {
        complete: () => {},
      },
    ],
  },

  // Ensure other scenes are correctly defined...
};

const animationEntities = [
  document.querySelector("#vm-entity"),
  document.querySelector("#wallet-entity"),
  document.querySelector("#coin-entity"),
  document.querySelector("#ball-entity"),
  document.querySelector("#boat-entity"),
  document.querySelector("#bush-entity"),
  document.querySelector("#shop-entity"),
  document.querySelector("#clock-brockenA-entity"),
  document.querySelector("#clock-brocken-entity"),
  document.querySelector("#clock-handle1-entity"),
  document.querySelector("#clock-handle2-entity"),
  document.querySelector("#clock-ticking-entity"),
  document.querySelector("#chest-entity"),
  document.querySelector("#ticket-entity"),
  document.querySelector("#bus-entity"),
  document.querySelector("#hg1-entity"),
  document.querySelector("#hg2-entity"),
  document.querySelector("#hg3-entity"),
  document.querySelector("#hg4-entity"),
  document.querySelector("#key1-entity"),
  document.querySelector("#key2-entity"),
  document.querySelector("#key3-entity"),
];

function playAnimation(animationName, markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity) {
    // Remove any existing animation-mixer component
    entity.removeAttribute("animation-mixer");

    // Add the animation-mixer component with the specified animation clip
    entity.setAttribute("animation-mixer", {
      clip: animationName,
      loop: "once",
      clampWhenFinished: true,
    });
  }
}

function playMarkerAnimation(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const animationName = getAnimationName(markerId);
    if (animationName) {
      playAnimation(animationName, markerId);
    }
  }
}

function resetMarkerAnimation(markerId) {
  const entity = document.querySelector(`#${markerId}-entity`);
  if (entity && entity.getAttribute("animation-mixer")) {
    entity.removeAttribute("animation-mixer");
  }
}

function getAnimationName(markerId) {
  const currentActions = scenes[currentScene].actions[subSceneIndex];
  if (currentActions && currentActions[markerId]) {
    const actionFunc = currentActions[markerId];
    const actionString = actionFunc.toString();
    const match = actionString.match(/playAnimation\(["'](.+)["']/);
    if (match) {
      return match[1];
    }
  }
  return null;
}

//------------------------------------------------------------------
//  Funkcijas
//------------------------------------------------------------------

function resetAnimationStates() {
  Object.keys(animationStates).forEach((key) => (animationStates[key] = false));
}

function resetAnimationState(markerId) {
  const animationKey = `${currentScene}_${subSceneIndex}_${markerId}`;
  animationStates[animationKey] = false;
}

function resetToSubScene(newIndex) {
  subSceneIndex = newIndex;
}

function completeScene(nextScene, ...actions) {
  console.log(`Completing scene: ${currentScene}, moving to: ${nextScene}`);
  currentScene = nextScene;
  subSceneIndex = 0; // Reset subSceneIndex for the new scene
  actions.forEach((action) => action());
}

function displayMessage(text) {
  const messageElement = document.getElementById("message");
  if (messageElement) {
    messageElement.innerText = text;
    messageElement.style.display = "block"; // Make sure it's visible

    setTimeout(() => {
      messageElement.style.display = "none";
    }, 4000); // 2000 milliseconds = 2 seconds
  } else {
    console.error("Message element not found");
  }
}

function setEntityVisibility(entityId, visible) {
  const entity = document.querySelector(`#${entityId}`);
  if (entity) {
    entity.setAttribute("visible", visible);
  } else {
    console.error(`Element #${entityId} not found`);
  }
}

startGame();
setupEventListeners();

function resetScene() {
  subSceneIndex = 0;
  // resetBushPosition();
  // resetClockPosition();
}

function resetGame() {
  // resetBushPosition();
  // resetClockPosition();

  setEntityVisibility("coin-entity", false);
  setEntityVisibility("ball-entity", false);
  setEntityVisibility("boat-entity", false);
  setEntityVisibility("bush-entity", false);
  setEntityVisibility("shop-entity", false);
  setEntityVisibility("clock-brockenA-entity", false);
  setEntityVisibility("clock-brocken-entity", false);
  setEntityVisibility("clock-handle1-entity", false);
  setEntityVisibility("clock-handle2-entity", false);
  setEntityVisibility("clock-ticking-entity", false);
  setEntityVisibility("chest-entity", false);
  setEntityVisibility("ticket-entity", false);
  setEntityVisibility("bus-entity", false);
  setEntityVisibility("wallet-entity", false);
  setEntityVisibility("vm-entity", false);
  setEntityVisibility("hg1-entity", false);
  setEntityVisibility("hg2-entity", false);
  setEntityVisibility("hg3-entity", false);
  setEntityVisibility("hg4-entity", false);
  setEntityVisibility("key1-entity", false);
  setEntityVisibility("key2-entity", false);
  setEntityVisibility("key3-entity", false);
  currentScene = "scene0";
  subSceneIndex = 0;
}

AFRAME.registerComponent("continuous-tracking", {
  tick: function () {},
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
