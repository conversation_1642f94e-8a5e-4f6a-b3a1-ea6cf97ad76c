// Marker Definitions
var actionMarkers = {
    marker1: { element: document.querySelector("#marker1"), visible: false },
    marker2: { element: document.querySelector("#marker2"), visible: false },
    marker3: { element: document.querySelector("#marker3"), visible: false }
};

var currentScene = 'cylinder';
var subSceneIndex = 0;

// Initialize the game
resetGame();

// Event Listeners for Markers
Object.values(actionMarkers).forEach(marker => {
    marker.element.addEventListener("markerFound", () => {
        marker.visible = true;
        handleMarkerInteraction(marker.element.id);
    });
    marker.element.addEventListener("markerLost", () => {
        marker.visible = false;
    });
});

// Handle Marker Interactions
function handleMarkerInteraction(markerId) {
    const currentActions = scenes[currentScene].actions[subSceneIndex];
    if (currentActions[markerId]) {
        currentActions[markerId]();
    }
}

var scenes = {
    cylinder: {
        actions: [
            { // Subscene 1
                marker1: () => moveToSubScene(1, moveCylinderX),
                marker2: () => {}, // No action
                marker3: () => {}  // No action
            },
            { // Subscene 2
                marker1: () => {}, // No action
                marker2: () => moveToSubScene(2, moveCylinderZ),
                marker3: () => resetToSubScene(0)
            },
            { // Subscene 3
                marker1: () => {}, // No action
                marker2: () => {}, // No action
                marker3: () => completeScene('cube', hideCylinder, showCube)
            }
        ]
    },
    cube: {
      actions: [
            { // Subscene 1
                marker2: () => moveToSubScene(1, moveCubeX),
                marker1: () => {}, // No action
                marker3: () => {}  // No action
            },
            { // Subscene 2
                marker2: () => {}, // No action
                marker1: () => moveToSubScene(2, moveCubeZ),
                marker3: () => resetToSubScene(0)
            },
            { // Subscene 3
                marker2: () => {}, // No action
                marker1: () => {}, // No action
                marker3: () => completeScene('cone', hideCube)
            }
        ]
    },
   cone:
  {
  },
    // ... more scenes ...
};

function moveCylinderX() {
    let cylinder = document.querySelector("#cilindrs");
    let currentPosition = cylinder.getAttribute("position");
    cylinder.setAttribute("position", { x: currentPosition.x + 1, y: currentPosition.y, z: currentPosition.z });
}

function moveCylinderZ() {
  let cylinder = document.querySelector("#cilindrs");
    let currentPosition = cylinder.getAttribute("position");
    cylinder.setAttribute("position", { x: currentPosition.x, y: currentPosition.y, z: currentPosition.z - 1});
}

function moveCubeX() {
    let cube = document.querySelector("#kubs");
    let currentPosition = cube.getAttribute("position");
    cube.setAttribute("position", { x: currentPosition.x + 1, y: currentPosition.y, z: currentPosition.z });
}

function moveCubeZ() {
    let cube = document.querySelector("#kubs");
    let currentPosition = cube.getAttribute("position");
    cube.setAttribute("position", { x: currentPosition.x, y: currentPosition.y, z: currentPosition.z - 1});
}

function moveToSubScene(newIndex, action) {
    subSceneIndex = newIndex;
    if (action) action();
}

function resetToSubScene(newIndex) {
    subSceneIndex = newIndex;
    resetCylinderPosition(); // This will reset the position
}

function resetCylinderPosition() {
    // Reset cylinder position to the start position
    document.querySelector("#cilindrs").setAttribute("position", { x: 0, y: 0, z: 0 });
}

function completeScene(nextScene, ...actions) {
    currentScene = nextScene;
    subSceneIndex = 0;
    actions.forEach(action => action());
}
function hideCylinder() {
    document.querySelector("#cilindrs").setAttribute("visible", false);
}

function hideCube() {
    document.querySelector("#kubs").setAttribute("visible", false);
}

function showCube() {
    document.querySelector("#kubs").setAttribute("visible", true);
}

function resetScene() {
    subSceneIndex = 0;
    resetCylinderPosition();
    // Reset other objects if necessary
}

function resetGame() {
    document.querySelector("#kubs").setAttribute("visible", false); // Explicitly hide the cube
    document.querySelector("#cilindrs").setAttribute("visible", true); // Show cylinder initially
    resetCylinderPosition();

    // Reset the scene and subscene indexes
    currentScene = 'cylinder';
    subSceneIndex = 0;

    // Reset any other game states as needed
}

// A-Frame Component for Continuous Tracking
AFRAME.registerComponent("continuous-tracking", {
    tick: function() {
        // Optional: Continuous tracking logic
    }
});

document.querySelector("a-scene").setAttribute("continuous-tracking", "");
